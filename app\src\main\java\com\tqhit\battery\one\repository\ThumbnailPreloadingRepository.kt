package com.tqhit.battery.one.repository

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.tqhit.adlib.sdk.data.local.PreferencesHelper
import com.tqhit.battery.one.fragment.main.animation.data.PreloadedThumbnailItem
import com.tqhit.battery.one.fragment.main.animation.data.ThumbnailItem
import com.tqhit.battery.one.fragment.main.animation.data.ThumbnailPreloadResult
import com.tqhit.battery.one.fragment.main.animation.data.ThumbnailPreloadStatus
import com.tqhit.battery.one.manager.thumbnail.ThumbnailFileManager
import com.tqhit.battery.one.service.thumbnail.ThumbnailPreloader
import com.tqhit.battery.one.utils.BatteryLogger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for managing thumbnail preloading operations.
 * Provides a clean interface for thumbnail preloading with state management and persistence.
 * 
 * Following SOLID principles:
 * - Single Responsibility: Only handles thumbnail preloading repository operations
 * - Open/Closed: Extensible for different preloading strategies
 * - Dependency Inversion: Depends on abstractions (ThumbnailPreloader, ThumbnailFileManager)
 */
@Singleton
class ThumbnailPreloadingRepository @Inject constructor(
    private val thumbnailPreloader: ThumbnailPreloader,
    private val fileManager: ThumbnailFileManager,
    private val preferencesHelper: PreferencesHelper,
    private val gson: Gson
) {
    companion object {
        private const val TAG = "ThumbnailPreloadingRepository"
        private const val PREF_PRELOADED_THUMBNAILS = "preloaded_thumbnails"
        private const val PREF_LAST_THUMBNAIL_PRELOAD = "last_thumbnail_preload_timestamp"
    }
    
    // State flow for preloading status
    private val _preloadingStatus = MutableStateFlow<ThumbnailPreloadingStatus>(ThumbnailPreloadingStatus.Idle)
    val preloadingStatus: StateFlow<ThumbnailPreloadingStatus> = _preloadingStatus.asStateFlow()
    
    /**
     * PRELOAD_DISABLED: Thumbnail preloading method commented out to reduce resource consumption.
     * This method previously initiated thumbnail preloading during app startup.
     *
     * Thumbnail loading will still work on-demand when needed.
     * To re-enable preloading, uncomment this method and related calls.
     */
    /*
    suspend fun initiatePreloading(thumbnails: List<ThumbnailItem>): ThumbnailPreloadingResult = withContext(Dispatchers.IO) {
        try {
            if (thumbnails.isEmpty()) {
                BatteryLogger.w(TAG, "No thumbnails provided for preloading")
                return@withContext ThumbnailPreloadingResult.NoThumbnailsProvided
            }

            BatteryLogger.d(TAG, "Initiating thumbnail preloading for ${thumbnails.size} thumbnails")
            _preloadingStatus.value = ThumbnailPreloadingStatus.InProgress(thumbnails.size, 0)

            // Start preloading
            val results = thumbnailPreloader.preloadThumbnails(thumbnails)

            // Process results
            val successfulResults = results.filterIsInstance<ThumbnailPreloadResult.Success>()
            val failedResults = results.filterIsInstance<ThumbnailPreloadResult.Failure>()
            val existingResults = results.filterIsInstance<ThumbnailPreloadResult.AlreadyExists>()

            // Save successful preloads to preferences
            val allPreloadedThumbnails = successfulResults.map { it.preloadedThumbnail } +
                                       existingResults.map { it.preloadedThumbnail }
            savePreloadedThumbnails(allPreloadedThumbnails)

            // Update last preload timestamp
            preferencesHelper.saveLong(PREF_LAST_THUMBNAIL_PRELOAD, System.currentTimeMillis())

            // Update status
            _preloadingStatus.value = ThumbnailPreloadingStatus.Completed(
                total = thumbnails.size,
                successful = successfulResults.size + existingResults.size,
                failed = failedResults.size
            )

            BatteryLogger.d(TAG, "Thumbnail preloading completed. Success: ${successfulResults.size}, " +
                    "Existing: ${existingResults.size}, Failed: ${failedResults.size}")

            // Return appropriate result
            when {
                failedResults.isEmpty() -> ThumbnailPreloadingResult.Success(
                    successfulCount = successfulResults.size,
                    existingCount = existingResults.size,
                    results = results
                )
                successfulResults.isEmpty() && existingResults.isEmpty() -> ThumbnailPreloadingResult.AllFailed(
                    failedResults = failedResults
                )
                else -> ThumbnailPreloadingResult.PartialSuccess(
                    successfulCount = successfulResults.size,
                    existingCount = existingResults.size,
                    failedCount = failedResults.size,
                    results = results
                )
            }
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error during thumbnail preloading", e)
            _preloadingStatus.value = ThumbnailPreloadingStatus.Error(e.message ?: "Unknown error")
            ThumbnailPreloadingResult.Error(e)
        }
    }
    */
    
    /**
     * Gets a preloaded thumbnail by URL.
     */
    suspend fun getPreloadedThumbnail(thumbnailUrl: String): PreloadedThumbnailItem? = withContext(Dispatchers.IO) {
        try {
            fileManager.getPreloadedThumbnail(thumbnailUrl)
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error getting preloaded thumbnail for $thumbnailUrl", e)
            null
        }
    }
    
    /**
     * Checks if a thumbnail is preloaded and available.
     */
    suspend fun isThumbnailPreloaded(thumbnailUrl: String): Boolean = withContext(Dispatchers.IO) {
        try {
            thumbnailPreloader.isThumbnailPreloaded(thumbnailUrl)
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error checking thumbnail preload status for $thumbnailUrl", e)
            false
        }
    }
    
    /**
     * Gets the local file path for a preloaded thumbnail.
     */
    suspend fun getPreloadedThumbnailPath(thumbnailUrl: String): String? = withContext(Dispatchers.IO) {
        try {
            thumbnailPreloader.getPreloadedThumbnailPath(thumbnailUrl)
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error getting preloaded thumbnail path for $thumbnailUrl", e)
            null
        }
    }
    
    /**
     * Gets thumbnail preloading statistics.
     */
    suspend fun getThumbnailPreloadingStats(): ThumbnailPreloadingStats = withContext(Dispatchers.IO) {
        try {
            val preloadedCount = fileManager.getPreloadedThumbnailCount()
            val totalSize = fileManager.getTotalThumbnailSize()
            val lastPreloadTimestamp = preferencesHelper.getLong(PREF_LAST_THUMBNAIL_PRELOAD, 0L)
            
            ThumbnailPreloadingStats(
                preloadedCount = preloadedCount,
                totalSizeBytes = totalSize,
                lastPreloadTimestamp = lastPreloadTimestamp
            )
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error getting thumbnail preloading stats", e)
            ThumbnailPreloadingStats(0, 0L, 0L)
        }
    }
    
    /**
     * Cleans up old or invalid thumbnail files.
     */
    suspend fun cleanupThumbnailFiles(): Int = withContext(Dispatchers.IO) {
        try {
            val cleanedCount = fileManager.cleanupThumbnailFiles()
            BatteryLogger.d(TAG, "Cleaned up $cleanedCount thumbnail files")
            
            // Update preferences to remove references to cleaned files
            updatePreferencesAfterCleanup()
            
            cleanedCount
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error during thumbnail cleanup", e)
            0
        }
    }
    
    /**
     * Saves preloaded thumbnail metadata to preferences.
     */
    private fun savePreloadedThumbnails(preloadedThumbnails: List<PreloadedThumbnailItem>) {
        try {
            val existingThumbnails = getPreloadedThumbnailsFromPreferences().toMutableList()
            
            // Add new thumbnails, avoiding duplicates
            preloadedThumbnails.forEach { newThumbnail ->
                val existingIndex = existingThumbnails.indexOfFirst { 
                    it.thumbnailUrl == newThumbnail.thumbnailUrl 
                }
                if (existingIndex >= 0) {
                    existingThumbnails[existingIndex] = newThumbnail
                } else {
                    existingThumbnails.add(newThumbnail)
                }
            }
            
            val json = gson.toJson(existingThumbnails)
            preferencesHelper.saveString(PREF_PRELOADED_THUMBNAILS, json)
            
            BatteryLogger.d(TAG, "Saved ${existingThumbnails.size} preloaded thumbnails to preferences")
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error saving preloaded thumbnails to preferences", e)
        }
    }
    
    /**
     * Gets preloaded thumbnail metadata from preferences.
     */
    private fun getPreloadedThumbnailsFromPreferences(): List<PreloadedThumbnailItem> {
        return try {
            val json = preferencesHelper.getString(PREF_PRELOADED_THUMBNAILS, "")
            if (json.isBlank()) {
                emptyList()
            } else {
                val type = object : TypeToken<List<PreloadedThumbnailItem>>() {}.type
                gson.fromJson(json, type) ?: emptyList()
            }
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error reading preloaded thumbnails from preferences", e)
            emptyList()
        }
    }
    
    /**
     * Updates preferences after cleanup to remove references to deleted files.
     */
    private suspend fun updatePreferencesAfterCleanup() {
        try {
            val savedThumbnails = getPreloadedThumbnailsFromPreferences()
            val validThumbnails = savedThumbnails.filter { thumbnail ->
                java.io.File(thumbnail.localFilePath).exists()
            }
            
            if (validThumbnails.size != savedThumbnails.size) {
                val json = gson.toJson(validThumbnails)
                preferencesHelper.saveString(PREF_PRELOADED_THUMBNAILS, json)
                BatteryLogger.d(TAG, "Updated preferences after cleanup. Removed ${savedThumbnails.size - validThumbnails.size} invalid references")
            }
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error updating preferences after cleanup", e)
        }
    }
}

/**
 * Sealed class representing thumbnail preloading status
 */
sealed class ThumbnailPreloadingStatus {
    object Idle : ThumbnailPreloadingStatus()
    data class InProgress(val total: Int, val completed: Int) : ThumbnailPreloadingStatus()
    data class Completed(val total: Int, val successful: Int, val failed: Int) : ThumbnailPreloadingStatus()
    data class Error(val message: String) : ThumbnailPreloadingStatus()
}

/**
 * Sealed class representing thumbnail preloading results
 */
sealed class ThumbnailPreloadingResult {
    data class Success(
        val successfulCount: Int,
        val existingCount: Int,
        val results: List<ThumbnailPreloadResult>
    ) : ThumbnailPreloadingResult()
    
    data class PartialSuccess(
        val successfulCount: Int,
        val existingCount: Int,
        val failedCount: Int,
        val results: List<ThumbnailPreloadResult>
    ) : ThumbnailPreloadingResult()
    
    data class AllFailed(
        val failedResults: List<ThumbnailPreloadResult.Failure>
    ) : ThumbnailPreloadingResult()
    
    object NoThumbnailsProvided : ThumbnailPreloadingResult()
    
    data class Error(val exception: Throwable) : ThumbnailPreloadingResult()
}

/**
 * Data class for thumbnail preloading statistics
 */
data class ThumbnailPreloadingStats(
    val preloadedCount: Int,
    val totalSizeBytes: Long,
    val lastPreloadTimestamp: Long
)
