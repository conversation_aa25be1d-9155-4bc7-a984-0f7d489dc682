package com.tqhit.battery.one.repository

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.tqhit.adlib.sdk.data.local.PreferencesHelper
import com.tqhit.battery.one.fragment.main.animation.data.AnimationItem
import com.tqhit.battery.one.fragment.main.animation.data.PreloadResult
import com.tqhit.battery.one.fragment.main.animation.data.PreloadedAnimationItem
import com.tqhit.battery.one.fragment.main.animation.data.PreloadStatus
import com.tqhit.battery.one.manager.animation.AnimationFileManager
import com.tqhit.battery.one.service.animation.AnimationPreloader
import com.tqhit.battery.one.service.animation.PreloadingStatus
import com.tqhit.battery.one.utils.BatteryLogger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for managing animation preloading operations.
 * Coordinates between preloader service, file manager, and persistent storage.
 * 
 * Following Repository Pattern and SOLID principles:
 * - Single Responsibility: Manages preloading data and operations
 * - Open/Closed: Extensible for different storage backends
 * - Dependency Inversion: Depends on abstractions
 */
@Singleton
class AnimationPreloadingRepository @Inject constructor(
    private val preferencesHelper: PreferencesHelper,
    private val animationPreloader: AnimationPreloader,
    private val fileManager: AnimationFileManager,
    private val gson: Gson
) {
    companion object {
        private const val TAG = "AnimationPreloadingRepository"
        private const val KEY_PRELOADED_ANIMATIONS = "preloaded_animations"
        private const val KEY_LAST_PRELOAD_TIMESTAMP = "last_preload_timestamp"
        private const val KEY_PRELOAD_VERSION = "preload_version"
        
        // Version for tracking preload data format changes
        private const val CURRENT_PRELOAD_VERSION = 1
        
        // Preload refresh interval (24 hours)
        private const val PRELOAD_REFRESH_INTERVAL_MS = 24 * 60 * 60 * 1000L
    }
    
    // StateFlow for observing preloading status
    private val _preloadingStatus = MutableStateFlow<PreloadingStatus?>(null)
    val preloadingStatus: StateFlow<PreloadingStatus?> = _preloadingStatus.asStateFlow()
    
    /**
     * PRELOAD_DISABLED: Animation preloading method commented out to reduce resource consumption.
     * This method previously initiated preloading of the first 6 animations during app startup.
     *
     * Animation loading will still work on-demand when needed.
     * To re-enable preloading, uncomment this method and related calls.
     */
    /*
    suspend fun initiatePreloading(animations: List<AnimationItem>): PreloadingResult = withContext(Dispatchers.IO) {
        try {
            BatteryLogger.d(TAG, "Initiating preloading for ${animations.size} animations")

            if (animations.isEmpty()) {
                BatteryLogger.w(TAG, "No animations provided for preloading")
                return@withContext PreloadingResult.NoAnimationsProvided
            }

            // Check if preloading is needed
            if (!shouldPreload()) {
                BatteryLogger.d(TAG, "Preloading not needed, using existing files")
                return@withContext PreloadingResult.AlreadyUpToDate
            }

            // Update status to indicate preloading started
            _preloadingStatus.value = animationPreloader.getPreloadingStatus()

            // Start preloading
            val results = animationPreloader.preloadAnimations(animations)

            // Process results and update storage
            val successfulPreloads = mutableListOf<PreloadedAnimationItem>()
            val failures = mutableListOf<PreloadResult.Failure>()

            results.forEach { result ->
                when (result) {
                    is PreloadResult.Success -> {
                        successfulPreloads.add(result.preloadedItem)
                        BatteryLogger.d(TAG, "Successfully preloaded: ${result.preloadedItem.mediaOriginal}")
                    }
                    is PreloadResult.AlreadyExists -> {
                        successfulPreloads.add(result.preloadedItem)
                        BatteryLogger.d(TAG, "Already exists: ${result.preloadedItem.mediaOriginal}")
                    }
                    is PreloadResult.Failure -> {
                        failures.add(result)
                        BatteryLogger.w(TAG, "Failed to preload: ${result.mediaOriginal} - ${result.errorMessage}")
                    }
                }
            }

            // Save successful preloads to preferences
            if (successfulPreloads.isNotEmpty()) {
                savePreloadedAnimations(successfulPreloads)
                updateLastPreloadTimestamp()
            }

            // Clean up old files
            val cleanedCount = fileManager.cleanupOldFiles()
            BatteryLogger.d(TAG, "Cleaned up $cleanedCount old files")

            // Update status to indicate completion
            _preloadingStatus.value = null

            // Return result summary
            when {
                failures.isEmpty() -> PreloadingResult.Success(successfulPreloads.size)
                successfulPreloads.isEmpty() -> PreloadingResult.AllFailed(failures)
                else -> PreloadingResult.PartialSuccess(successfulPreloads.size, failures)
            }

        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error during preloading initiation", e)
            _preloadingStatus.value = null
            PreloadingResult.Error(e)
        }
    }
    */
    
    /**
     * Gets a preloaded animation file if available.
     * Returns null if not preloaded or file is invalid.
     */
    suspend fun getPreloadedAnimation(mediaUrl: String): PreloadedAnimationItem? = withContext(Dispatchers.IO) {
        try {
            // First check file system
            val fileSystemResult = fileManager.getPreloadedFile(mediaUrl)
            if (fileSystemResult != null && fileSystemResult.status == PreloadStatus.COMPLETED) {
                BatteryLogger.d(TAG, "Found preloaded animation in file system: $mediaUrl")
                return@withContext fileSystemResult
            }
            
            // Check preferences storage as backup
            val preloadedAnimations = getPreloadedAnimations()
            val preloadedItem = preloadedAnimations.find { it.mediaOriginal == mediaUrl }
            
            if (preloadedItem != null && preloadedItem.status == PreloadStatus.COMPLETED) {
                // Verify file still exists
                val file = java.io.File(preloadedItem.localFilePath)
                if (file.exists() && file.length() > 0) {
                    BatteryLogger.d(TAG, "Found preloaded animation in preferences: $mediaUrl")
                    return@withContext preloadedItem
                } else {
                    BatteryLogger.w(TAG, "Preloaded file no longer exists: ${preloadedItem.localFilePath}")
                    // Remove from preferences
                    removePreloadedAnimation(mediaUrl)
                }
            }
            
            BatteryLogger.d(TAG, "No preloaded animation found for: $mediaUrl")
            null
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error getting preloaded animation for $mediaUrl", e)
            null
        }
    }
    
    /**
     * Checks if preloading should be performed based on timestamp and existing files.
     */
    private fun shouldPreload(): Boolean {
        val lastPreloadTime = preferencesHelper.getLong(KEY_LAST_PRELOAD_TIMESTAMP, 0L)
        val currentTime = System.currentTimeMillis()
        val timeSinceLastPreload = currentTime - lastPreloadTime
        
        val shouldPreloadByTime = timeSinceLastPreload > PRELOAD_REFRESH_INTERVAL_MS
        val hasValidVersion = preferencesHelper.getInt(KEY_PRELOAD_VERSION, 0) == CURRENT_PRELOAD_VERSION
        
        BatteryLogger.d(TAG, "Preload check - Time since last: ${timeSinceLastPreload}ms, " +
                "Should preload by time: $shouldPreloadByTime, Has valid version: $hasValidVersion")
        
        return shouldPreloadByTime || !hasValidVersion
    }
    
    /**
     * Saves preloaded animations to preferences.
     */
    private fun savePreloadedAnimations(animations: List<PreloadedAnimationItem>) {
        try {
            val json = gson.toJson(animations)
            preferencesHelper.saveString(KEY_PRELOADED_ANIMATIONS, json)
            preferencesHelper.saveInt(KEY_PRELOAD_VERSION, CURRENT_PRELOAD_VERSION)
            BatteryLogger.d(TAG, "Saved ${animations.size} preloaded animations to preferences")
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error saving preloaded animations", e)
        }
    }
    
    /**
     * Gets preloaded animations from preferences.
     */
    private fun getPreloadedAnimations(): List<PreloadedAnimationItem> {
        return try {
            val json = preferencesHelper.getString(KEY_PRELOADED_ANIMATIONS, "")
            if (json.isEmpty()) {
                emptyList()
            } else {
                gson.fromJson(json, object : TypeToken<List<PreloadedAnimationItem>>() {}.type)
            }
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error loading preloaded animations from preferences", e)
            emptyList()
        }
    }
    
    /**
     * Removes a specific preloaded animation from preferences.
     */
    private fun removePreloadedAnimation(mediaUrl: String) {
        try {
            val currentAnimations = getPreloadedAnimations()
            val updatedAnimations = currentAnimations.filter { it.mediaOriginal != mediaUrl }
            savePreloadedAnimations(updatedAnimations)
            BatteryLogger.d(TAG, "Removed preloaded animation from preferences: $mediaUrl")
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error removing preloaded animation", e)
        }
    }
    
    /**
     * Updates the last preload timestamp.
     */
    private fun updateLastPreloadTimestamp() {
        preferencesHelper.saveLong(KEY_LAST_PRELOAD_TIMESTAMP, System.currentTimeMillis())
    }
    
    /**
     * Gets preloading statistics.
     */
    suspend fun getPreloadingStats(): PreloadingStats = withContext(Dispatchers.IO) {
        try {
            val fileCount = fileManager.getPreloadedFileCount()
            val totalSize = fileManager.getTotalPreloadedSize()
            val lastPreloadTime = preferencesHelper.getLong(KEY_LAST_PRELOAD_TIMESTAMP, 0L)
            
            PreloadingStats(
                preloadedFileCount = fileCount,
                totalSizeBytes = totalSize,
                lastPreloadTimestamp = lastPreloadTime
            )
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error getting preloading stats", e)
            PreloadingStats(0, 0L, 0L)
        }
    }
    
    /**
     * Clears all preloaded data and files.
     */
    suspend fun clearAllPreloadedData(): Boolean = withContext(Dispatchers.IO) {
        try {
            // Clear preferences
            preferencesHelper.saveString(KEY_PRELOADED_ANIMATIONS, "")
            preferencesHelper.saveLong(KEY_LAST_PRELOAD_TIMESTAMP, 0L)
            preferencesHelper.saveInt(KEY_PRELOAD_VERSION, 0)
            
            // Clean up all files
            val cleanedCount = fileManager.cleanupOldFiles()
            
            BatteryLogger.d(TAG, "Cleared all preloaded data and $cleanedCount files")
            true
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error clearing preloaded data", e)
            false
        }
    }
}

/**
 * Sealed class representing the result of a preloading operation.
 */
sealed class PreloadingResult {
    object NoAnimationsProvided : PreloadingResult()
    object AlreadyUpToDate : PreloadingResult()
    data class Success(val preloadedCount: Int) : PreloadingResult()
    data class PartialSuccess(val successCount: Int, val failures: List<PreloadResult.Failure>) : PreloadingResult()
    data class AllFailed(val failures: List<PreloadResult.Failure>) : PreloadingResult()
    data class Error(val exception: Throwable) : PreloadingResult()
}

/**
 * Data class containing preloading statistics.
 */
data class PreloadingStats(
    val preloadedFileCount: Int,
    val totalSizeBytes: Long,
    val lastPreloadTimestamp: Long
)
