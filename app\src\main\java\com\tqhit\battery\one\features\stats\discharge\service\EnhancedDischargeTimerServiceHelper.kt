package com.tqhit.battery.one.features.stats.discharge.service

import android.app.ActivityManager
import android.app.ForegroundServiceStartNotAllowedException
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import androidx.core.content.ContextCompat
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Helper class for managing the EnhancedDischargeTimerService
 * Follows the CoreBatteryStatsService architecture pattern
 */
@Singleton
class EnhancedDischargeTimerServiceHelper @Inject constructor(
    @ApplicationContext private val context: Context
) {
    companion object {
        private const val TAG = "EnhancedDischargeHelper"
        private const val FOREGROUND_TAG = "EnhancedDischarge_FG_Helper"
        private const val ERROR_TAG = "EnhancedDischarge_ERR_Helper"
        private const val FALLBACK_TAG = "EnhancedDischarge_FB_Helper"
    }
    
    /**
     * Checks if the EnhancedDischargeTimerService is currently running
     */
    fun isServiceRunning(): Boolean {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val isRunning = activityManager.getRunningServices(Integer.MAX_VALUE)
            .any { it.service.className == EnhancedDischargeTimerService::class.java.name }
        
        Log.d(TAG, "isServiceRunning: $isRunning")
        return isRunning
    }
    
    /**
     * Starts the EnhancedDischargeTimerService with improved reliability and comprehensive error handling
     */
    fun startService() {
        Log.d(TAG, "Starting EnhancedDischargeTimerService")
        val serviceIntent = Intent(context, EnhancedDischargeTimerService::class.java).apply {
            action = EnhancedDischargeTimerService.ACTION_START_SERVICE
        }

        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                Log.d(FOREGROUND_TAG, "Starting as foreground service (Android O+)")
                ContextCompat.startForegroundService(context, serviceIntent)
            } else {
                Log.d(TAG, "Starting as background service (pre-Android O)")
                context.startService(serviceIntent)
            }
            Log.d(TAG, "EnhancedDischargeTimerService started successfully")
        } catch (e: Exception) {
            handleServiceStartFailure(e)
        }
    }

    /**
     * Handles service startup failures with appropriate fallback mechanisms.
     * Specifically handles Android 12+ ForegroundServiceStartNotAllowedException.
     */
    private fun handleServiceStartFailure(exception: Exception) {
        when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.S &&
            exception is ForegroundServiceStartNotAllowedException -> {
                Log.w(ERROR_TAG, "ForegroundServiceStartNotAllowedException caught - app likely in background")
                Log.w(ERROR_TAG, "Android 12+ background service restrictions prevent foreground service startup")
                handleAndroid12PlusRestrictions()
            }
            else -> {
                Log.e(ERROR_TAG, "Failed to start EnhancedDischargeTimerService", exception)
                // For other exceptions, try fallback mechanisms
                tryFallbackServiceStart()
            }
        }
    }

    /**
     * Handles Android 12+ specific restrictions for foreground service startup.
     */
    private fun handleAndroid12PlusRestrictions() {
        Log.w(FALLBACK_TAG, "Android 12+ detected - foreground service start blocked")
        Log.w(FALLBACK_TAG, "This is expected when app is in background context")
        Log.w(FALLBACK_TAG, "Service will be started when app returns to foreground")

        // Note: The service will be started automatically when app comes to foreground
        // via ServiceManager's foreground detection logic
    }

    /**
     * Attempts fallback service startup mechanisms.
     */
    private fun tryFallbackServiceStart() {
        Log.d(FALLBACK_TAG, "Attempting fallback service start mechanisms")

        // Try regular service start as fallback
        tryRegularServiceStart()
    }

    /**
     * Attempts to start the service as a regular service (not foreground).
     * This may work in some cases where foreground service is restricted.
     */
    private fun tryRegularServiceStart() {
        try {
            Log.d(FALLBACK_TAG, "Attempting regular service start as fallback")
            val intent = Intent(context, EnhancedDischargeTimerService::class.java).apply {
                action = EnhancedDischargeTimerService.ACTION_START_SERVICE
            }
            context.startService(intent)
            Log.d(FALLBACK_TAG, "Successfully started service as regular service")
        } catch (e: Exception) {
            Log.e(FALLBACK_TAG, "Regular service start also failed", e)
            logFallbackGuidance()
        }
    }

    /**
     * Logs guidance for when all service startup methods fail.
     */
    private fun logFallbackGuidance() {
        Log.w(FALLBACK_TAG, "All service startup methods failed")
        Log.w(FALLBACK_TAG, "Service will be retried when app returns to foreground")
        Log.w(FALLBACK_TAG, "Consider checking battery optimization settings if issues persist")
    }

    /**
     * Stops the EnhancedDischargeTimerService
     */
    fun stopService() {
        Log.d(TAG, "Stopping EnhancedDischargeTimerService")
        val serviceIntent = Intent(context, EnhancedDischargeTimerService::class.java).apply {
            action = EnhancedDischargeTimerService.ACTION_STOP_SERVICE
        }
        
        try {
            context.startService(serviceIntent)
            Log.d(TAG, "EnhancedDischargeTimerService stop command sent")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to stop EnhancedDischargeTimerService", e)
            
            // Fallback: try direct stop
            try {
                val fallbackIntent = Intent(context, EnhancedDischargeTimerService::class.java)
                context.stopService(fallbackIntent)
                Log.d(TAG, "EnhancedDischargeTimerService stopped via fallback method")
            } catch (fallbackException: Exception) {
                Log.e(TAG, "Fallback stop also failed", fallbackException)
            }
        }
    }
    
    /**
     * Restart the service (stop and start)
     */
    fun restartService() {
        Log.d(TAG, "Restarting EnhancedDischargeTimerService")
        stopService()
        
        // Give some time for the service to stop
        Thread.sleep(1000)
        
        startService()
    }
    
    /**
     * Get service status information for debugging
     */
    fun getServiceStatus(): String {
        val isRunning = isServiceRunning()
        return "EnhancedDischargeTimerService: ${if (isRunning) "RUNNING" else "STOPPED"}"
    }
}
