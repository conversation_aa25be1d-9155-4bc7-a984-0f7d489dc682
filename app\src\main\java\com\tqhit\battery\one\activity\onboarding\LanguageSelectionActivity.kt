package com.tqhit.battery.one.activity.onboarding

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import androidx.activity.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.applovin.mediation.nativeAds.MaxNativeAdView
import com.applovin.mediation.nativeAds.MaxNativeAdViewBinder
import com.tqhit.battery.one.R
import com.tqhit.battery.one.activity.starting.StartingActivity
import com.tqhit.battery.one.ads.core.ApplovinNativeAdManager
import com.tqhit.battery.one.base.LocaleAwareActivity
import com.tqhit.battery.one.databinding.ActivityLanguageSelectionBinding
import com.tqhit.battery.one.fragment.onboarding.LanguageSelectionUiState
import com.tqhit.battery.one.fragment.onboarding.LanguageSelectionViewModel
import com.tqhit.battery.one.viewmodel.AppViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Language Selection Activity for the onboarding flow.
 * 
 * Features:
 * - Full-screen language selection interface adapted from fragment layout
 * - MVI pattern state management with proper ViewBinding
 * - Language selection logic reused from SelectLanguageDialog
 * - Direct navigation to StartingActivity after language selection
 * - Material 3 design consistency with onboarding flow
 * - Proper activity lifecycle management and state persistence
 * 
 * Navigation Flow: SplashActivity → LanguageSelectionActivity → StartingActivity → MainActivity
 */
@AndroidEntryPoint
class LanguageSelectionActivity : LocaleAwareActivity<ActivityLanguageSelectionBinding>() {

    companion object {
        private const val TAG = "LanguageSelectionActivity"
    }

    private val maxNativeAdView: MaxNativeAdView by lazy {
        createNativeAdView()
    }

//    var isSelectedLanguage: Boolean = false
    private var isLoadedAds: Boolean = false
    private var preloadedNativeAdView: View? = null

    // ViewBinding implementation following established pattern
    override val binding by lazy {
        Log.d(TAG, "BINDING_INIT: Initializing ActivityLanguageSelectionBinding")
        try {
            ActivityLanguageSelectionBinding.inflate(layoutInflater)
        } catch (e: Exception) {
            Log.e(TAG, "BINDING_INIT: Critical error initializing ViewBinding", e)
            throw e
        }
    }

    // ViewModels
    private val appViewModel: AppViewModel by viewModels()
    private val languageSelectionViewModel: LanguageSelectionViewModel by viewModels()

    // Native Ad Manager
    @Inject lateinit var applovinNativeAdManager: ApplovinNativeAdManager

    override fun onCreate(savedInstanceState: Bundle?) {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "STARTUP_TIMING: LanguageSelectionActivity.onCreate() started at $startTime")

        super.onCreate(savedInstanceState)

        Log.d(TAG, "STARTUP_TIMING: LanguageSelectionActivity.onCreate() completed in ${System.currentTimeMillis() - startTime}ms")
    }

    override fun setupData() {
        super.setupData()
        Log.d(TAG, "LANGUAGE_SELECTION: LanguageSelectionActivity data setup completed")
    }

    override fun setupUI() {
        super.setupUI()

        val setupStartTime = System.currentTimeMillis()
        setupLanguageButtons()
        updateSelectedLanguageUI()
        setupNativeAd()
        Log.d(TAG, "STARTUP_TIMING: UI setup took ${System.currentTimeMillis() - setupStartTime}ms")

        Log.d(TAG, "LANGUAGE_SELECTION: LanguageSelectionActivity UI setup completed")
    }

    override fun setupListener() {
        super.setupListener()

        // Setup Next button listener
        setupNextButtonListener()

        // Observe ViewModel state
        observeLanguageSelectionState()

        Log.d(TAG, "LANGUAGE_SELECTION: LanguageSelectionActivity listeners setup completed")
    }

    /**
     * Sets up click listeners for all language option buttons.
     * Reuses the same language codes and logic from SelectLanguageDialog.
     */
    private fun setupLanguageButtons() {
        safeBindingAccess("setupLanguageButtons") { binding ->
            // Setup click listeners for each language option
            binding.de.setOnClickListener { selectLanguage("de") } // German
            binding.nl.setOnClickListener { selectLanguage("nl") } // Dutch
            binding.en.setOnClickListener { selectLanguage("en") } // English
            binding.es.setOnClickListener { selectLanguage("es") } // Spanish
            binding.fr.setOnClickListener { selectLanguage("fr") } // French
            binding.it.setOnClickListener { selectLanguage("it") } // Italian
            binding.hu.setOnClickListener { selectLanguage("hu") } // Hungarian
            binding.pl.setOnClickListener { selectLanguage("pl") } // Polish
            binding.pt.setOnClickListener { selectLanguage("pt") } // Portuguese
            binding.ro.setOnClickListener { selectLanguage("ro") } // Romanian
            binding.tr.setOnClickListener { selectLanguage("tr") } // Turkish
            binding.ru.setOnClickListener { selectLanguage("ru") } // Russian
            binding.ua.setOnClickListener { selectLanguage("uk") } // Ukrainian
            binding.ar.setOnClickListener { selectLanguage("ar") } // Arabic
            binding.zh.setOnClickListener { selectLanguage("zh") } // Chinese
            
            Log.d(TAG, "LANGUAGE_SELECTION: Language button listeners setup completed")
        }
    }

    /**
     * Updates the UI to show the currently selected language.
     * Uses the LanguageSelectionViewModel state for immediate visual feedback.
     */
    private fun updateSelectedLanguageUI() {
        safeBindingAccess("updateSelectedLanguageUI") { binding ->
            val selectedLanguage = languageSelectionViewModel.getCurrentSelectedLanguage()
            Log.d(TAG, "LANGUAGE_SELECTION: Updating UI for selected language: $selectedLanguage")

            // Helper function to reduce repetition
            fun setLanguageButtonState(
                button: View,
                languageCode: String,
                selectedRes: Int,
                unselectedRes: Int
            ) {
                button.isSelected = selectedLanguage == languageCode
                button.setBackgroundResource(if (button.isSelected) selectedRes else unselectedRes)
            }

            setLanguageButtonState(binding.de, "de", R.drawable.grey_block_selected_line_up, R.drawable.grey_block_line_up)
            setLanguageButtonState(binding.nl, "nl", R.drawable.grey_block_selected_color, R.drawable.grey_block_line)
            setLanguageButtonState(binding.en, "en", R.drawable.grey_block_selected_color, R.drawable.grey_block_line)
            setLanguageButtonState(binding.es, "es", R.drawable.grey_block_selected_color, R.drawable.grey_block_line)
            setLanguageButtonState(binding.fr, "fr", R.drawable.grey_block_selected_color, R.drawable.grey_block_line)
            setLanguageButtonState(binding.it, "it", R.drawable.grey_block_selected_color, R.drawable.grey_block_line)
            setLanguageButtonState(binding.hu, "hu", R.drawable.grey_block_selected_color, R.drawable.grey_block_line)
            setLanguageButtonState(binding.pl, "pl", R.drawable.grey_block_selected_color, R.drawable.grey_block_line)
            setLanguageButtonState(binding.pt, "pt", R.drawable.grey_block_selected_color, R.drawable.grey_block_line)
            setLanguageButtonState(binding.ro, "ro", R.drawable.grey_block_selected_color, R.drawable.grey_block_line)
            setLanguageButtonState(binding.tr, "tr", R.drawable.grey_block_selected_color, R.drawable.grey_block_line)
            setLanguageButtonState(binding.ru, "ru", R.drawable.grey_block_selected_color, R.drawable.grey_block_line)
            setLanguageButtonState(binding.ua, "uk", R.drawable.grey_block_selected_color, R.drawable.grey_block_line) // Note: UI uses "ua" but locale uses "uk"
            setLanguageButtonState(binding.ar, "ar", R.drawable.grey_block_selected_color, R.drawable.grey_block_line)
            setLanguageButtonState(binding.zh, "zh", R.drawable.grey_block_selected_line_down, R.drawable.grey_block_line_down)
        }
    }

    /**
     * Handles language selection and triggers navigation to StartingActivity.
     */
    private fun selectLanguage(languageCode: String) {
        Log.d(TAG, "LANGUAGE_SELECTION: User selected language: $languageCode")
        
        // Trigger language selection through ViewModel
        languageSelectionViewModel.selectLanguage(languageCode)
    }

    /**
     * Observes language selection state and handles navigation.
     */
    private fun observeLanguageSelectionState() {
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                languageSelectionViewModel.uiState.collect { state ->
                    when (state) {
                        is LanguageSelectionUiState.Idle -> {
                            Log.d(TAG, "LANGUAGE_SELECTION: State - Idle")
                            hideNextButton()
                        }
                        is LanguageSelectionUiState.LanguageSelected -> {
                            Log.d(TAG, "LANGUAGE_SELECTION: State - Language selected: ${state.languageCode}")
                            handleLanguageSelected(state.languageCode)
                            showPreloadedAd()
                            if (isLoadedAds)
                            {
                                Handler(Looper.getMainLooper()).postDelayed({
                                    showNextButton()
                                }, 1000)
                            }else{
                                Handler(Looper.getMainLooper()).postDelayed({
                                    showNextButton()
                                }, 2000)
                            }
                        }
                        is LanguageSelectionUiState.LanguageConfirmed -> {
                            Log.d(TAG, "LANGUAGE_SELECTION: State - Language confirmed: ${state.languageCode}")
                            handleLanguageConfirmed(state.languageCode)
                        }
                        is LanguageSelectionUiState.NavigatingToOnboarding -> {
                            Log.d(TAG, "LANGUAGE_SELECTION: State - Navigating to onboarding")
                            navigateToStartingActivity()
                        }
                    }
                }
            }
        }
    }

    /**
     * Handles the language selection (first step).
     * Updates UI selection but doesn't navigate yet.
     */
    private fun handleLanguageSelected(languageCode: String) {
        // Update UI to reflect selection
        updateSelectedLanguageUI()

        Log.d(TAG, "LANGUAGE_SELECTION: Language $languageCode selected - Next button shown")
    }

    /**
     * Handles the language confirmation (second step).
     * Saves language and triggers navigation.
     */
    private fun handleLanguageConfirmed(languageCode: String) {
        Log.d(TAG, "LANGUAGE_SELECTION: Starting language confirmation for: $languageCode")

        // Save language using AppViewModel
        appViewModel.setLanguage(languageCode)
        Log.d(TAG, "LANGUAGE_SELECTION: Language saved to repository")

        // Apply the new language to current activity
        appViewModel.setLocale(this, languageCode)
        Log.d(TAG, "LANGUAGE_SELECTION: Locale applied to current activity")

        // Also refresh the locale for immediate effect
        refreshLocale()
        Log.d(TAG, "LANGUAGE_SELECTION: Locale refreshed")

        // Verify the language was actually saved
        val verifyLanguage = appViewModel.getLanguage()
        Log.d(TAG, "LANGUAGE_SELECTION: Verification - saved language is: '$verifyLanguage'")

        // Trigger navigation after a short delay to ensure language is fully saved
        Handler(Looper.getMainLooper()).postDelayed({
            languageSelectionViewModel.proceedToOnboarding()
            Log.d(TAG, "LANGUAGE_SELECTION: Navigation triggered after delay")
        }, 200) // 200ms delay to ensure SharedPreferences write is complete

        Log.d(TAG, "LANGUAGE_SELECTION: Language $languageCode confirmed and applied, navigation scheduled")
    }

    /**
     * Navigates directly to StartingActivity for the original onboarding slides.
     */
    private fun navigateToStartingActivity() {
        try {
            val intent = Intent(this, StartingActivity::class.java)
            startActivity(intent)
            finish()
            
            Log.d(TAG, "LANGUAGE_SELECTION: Successfully navigated to StartingActivity")
        } catch (e: Exception) {
            Log.e(TAG, "LANGUAGE_SELECTION: Error navigating to StartingActivity", e)
        }
    }

    /**
     * Shows the Next button after language selection.
     */
    private fun showNextButton() {
        safeBindingAccess("showNextButton") { binding ->
            binding.nextButton.visibility = View.VISIBLE
            Log.d(TAG, "LANGUAGE_SELECTION: Next button shown")
        }
    }

    /**
     * Hides the Next button.
     */
    private fun hideNextButton() {
        safeBindingAccess("hideNextButton") { binding ->
            binding.nextButton.visibility = View.GONE
            Log.d(TAG, "LANGUAGE_SELECTION: Next button hidden")
        }
    }

    /**
     * Sets up the Next button click listener.
     */
    private fun setupNextButtonListener() {
        safeBindingAccess("setupNextButtonListener") { binding ->
            binding.nextButton.setOnClickListener {
                Log.d(TAG, "LANGUAGE_SELECTION: Next button clicked")
                languageSelectionViewModel.confirmLanguageSelection()
            }
            Log.d(TAG, "LANGUAGE_SELECTION: Next button listener setup completed")
        }
    }

    /**
     * Sets up native ad following StartingActivity pattern.
     */
    private fun setupNativeAd() {
        safeBindingAccess("setupNativeAd") { binding ->
            val container = binding.nativeAd

            applovinNativeAdManager.loadNativeAd(
                onAdLoaded = {
                    if (this.isDestroyed || this.isFinishing) {
                        return@loadNativeAd
                    }
                    applovinNativeAdManager.removeCachedNativeAd(it)
                    applovinNativeAdManager.render(it, maxNativeAdView)
                    preloadedNativeAdView = maxNativeAdView
                    isLoadedAds = true
                    Log.d(TAG, "NATIVE_AD: Native ad loaded successfully")
                },
                onAdLoadFailed = { errorMsg ->
                    isLoadedAds = true
                    Log.e(TAG, "NATIVE_AD: Failed to load native ad: $errorMsg")
                }
            )
        }
    }

    /**
     * Sets up show native ad following StartingActivity pattern.
     */
    private fun showPreloadedAd() {
        safeBindingAccess("showPreloadedAd") { binding ->

            val container = binding.nativeAd

            container.removeAllViews()
            container.hideShimmer()
            preloadedNativeAdView?.let {
                container.addView(it)
                Log.d(TAG, "NATIVE_AD: Preloaded ad shown to user")
            }
        }
    }




    /**
     * Creates native ad view following StartingActivity pattern.
     */
    private fun createNativeAdView(): MaxNativeAdView {
        val binder: MaxNativeAdViewBinder =
            MaxNativeAdViewBinder.Builder(R.layout.layout_native_ads)
                .setTitleTextViewId(R.id.title_text_view)
                .setBodyTextViewId(R.id.body_text_view)
                .setStarRatingContentViewGroupId(R.id.star_rating_view)
                .setAdvertiserTextViewId(R.id.advertiser_text_view)
                .setIconImageViewId(R.id.icon_image_view)
                .setMediaContentViewGroupId(R.id.media_view_container)
                .setOptionsContentViewGroupId(R.id.ad_options_view)
                .setCallToActionButtonId(R.id.cta_button)
                .build()
        return MaxNativeAdView(binder, this)
    }

    /**
     * Safe binding access method with error handling.
     * Follows the established pattern from other activities.
     */
    private fun safeBindingAccess(operation: String, action: (ActivityLanguageSelectionBinding) -> Unit) {
        try {
            action(binding)
        } catch (e: Exception) {
            Log.e(TAG, "BINDING_ACCESS: Error in $operation", e)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "LANGUAGE_SELECTION: LanguageSelectionActivity destroyed")
    }
}
