package com.tqhit.battery.one.base

import android.content.Context
import android.os.Bundle
import android.util.Log
import androidx.viewbinding.ViewBinding
import com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity
import com.tqhit.battery.one.repository.AppRepository
import javax.inject.Inject

/**
 * Base activity that properly handles locale context wrapping for language changes.
 * This ensures that language selection is immediately applied to all activities
 * during the onboarding flow, not just on app restart.
 * 
 * All activities that need proper language support should extend this class
 * instead of directly extending AdLibBaseActivity.
 */
abstract class LocaleAwareActivity<T : ViewBinding> : AdLibBaseActivity<T>() {
    
    @Inject
    open lateinit var appRepository: AppRepository
    
    companion object {
        private const val TAG = "LocaleAwareActivity"
    }

    /**
     * Override attachBaseContext to wrap the context with the selected locale.
     * This ensures that the activity uses the correct language from the moment it's created.
     */
    override fun attachBaseContext(newBase: Context?) {
        if (newBase == null) {
            super.attachBaseContext(newBase)
            return
        }

        try {
            // Create locale-aware context using the saved language preference
            val localeContext = createLocaleAwareContext(newBase)
            super.attachBaseContext(localeContext)
            
            Log.d(TAG, "Context wrapped with locale: ${localeContext.resources.configuration.locales[0]}")
        } catch (e: Exception) {
            Log.e(TAG, "Error wrapping context with locale, using default", e)
            super.attachBaseContext(newBase)
        }
    }

    /**
     * Creates a context with the appropriate locale based on saved preferences.
     * This method handles the case where AppRepository might not be injected yet
     * during early activity lifecycle.
     */
    private fun createLocaleAwareContext(baseContext: Context): Context {
        return try {
            // Try to use injected repository if available
            if (::appRepository.isInitialized) {
                appRepository.getLocaleContext(baseContext)
            } else {
                // Fallback: manually create locale context using SharedPreferences
                createFallbackLocaleContext(baseContext)
            }
        } catch (e: Exception) {
            Log.w(TAG, "Could not create locale context, using base context", e)
            baseContext
        }
    }

    /**
     * Fallback method to create locale context when dependency injection is not yet available.
     * This uses SharedPreferences directly to get the saved language.
     * Tries multiple common SharedPreferences file names to ensure compatibility.
     */
    private fun createFallbackLocaleContext(baseContext: Context): Context {
        val possiblePrefsNames = listOf(
            "ad_lib_pref",  // Correct AdLib SDK preferences file name
            "adlib_preferences",
            baseContext.packageName + "_preferences",
            "app_preferences",
            "default_preferences"
        )

        for (prefsName in possiblePrefsNames) {
            try {
                val sharedPrefs = baseContext.getSharedPreferences(prefsName, Context.MODE_PRIVATE)
                val savedLanguage = sharedPrefs.getString("language", "") ?: ""

                Log.d(TAG, "Checking SharedPreferences '$prefsName' for language: '$savedLanguage'")

                if (savedLanguage.isNotEmpty()) {
                    val locale = java.util.Locale(savedLanguage)
                    val config = android.content.res.Configuration(baseContext.resources.configuration)
                    config.setLocale(locale)

                    Log.d(TAG, "Created fallback locale context for language: $savedLanguage from prefs: $prefsName")
                    return baseContext.createConfigurationContext(config)
                }
            } catch (e: Exception) {
                Log.w(TAG, "Error checking SharedPreferences '$prefsName'", e)
            }
        }

        Log.d(TAG, "No saved language found in any SharedPreferences, using base context")
        return baseContext
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Log current locale for debugging
        val currentLocale = resources.configuration.locales[0]
        Log.d(TAG, "${this::class.simpleName} created with locale: $currentLocale")
    }

    /**
     * Helper method to refresh the activity's locale if language is changed at runtime.
     * This can be called after language selection to immediately apply changes.
     */
    protected fun refreshLocale() {
        try {
            if (::appRepository.isInitialized) {
                val savedLanguage = appRepository.getLanguage()
                if (savedLanguage.isNotEmpty()) {
                    appRepository.setLocale(this, savedLanguage)
                    Log.d(TAG, "Locale refreshed for ${this::class.simpleName}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error refreshing locale", e)
        }
    }
}
