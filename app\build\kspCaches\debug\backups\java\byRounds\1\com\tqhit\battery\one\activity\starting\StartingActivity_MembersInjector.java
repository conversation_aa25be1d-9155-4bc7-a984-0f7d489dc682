package com.tqhit.battery.one.activity.starting;

import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper;
import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager;
import com.tqhit.battery.one.ads.core.ApplovinNativeAdManager;
import com.tqhit.battery.one.base.LocaleAwareActivity_MembersInjector;
import com.tqhit.battery.one.repository.AppRepository;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class StartingActivity_MembersInjector implements MembersInjector<StartingActivity> {
  private final Provider<AppRepository> appRepositoryProvider;

  private final Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider;

  private final Provider<ApplovinNativeAdManager> applovinNativeAdManagerProvider;

  private final Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider;

  public StartingActivity_MembersInjector(Provider<AppRepository> appRepositoryProvider,
      Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider,
      Provider<ApplovinNativeAdManager> applovinNativeAdManagerProvider,
      Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider) {
    this.appRepositoryProvider = appRepositoryProvider;
    this.applovinInterstitialAdManagerProvider = applovinInterstitialAdManagerProvider;
    this.applovinNativeAdManagerProvider = applovinNativeAdManagerProvider;
    this.remoteConfigHelperProvider = remoteConfigHelperProvider;
  }

  public static MembersInjector<StartingActivity> create(
      Provider<AppRepository> appRepositoryProvider,
      Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider,
      Provider<ApplovinNativeAdManager> applovinNativeAdManagerProvider,
      Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider) {
    return new StartingActivity_MembersInjector(appRepositoryProvider, applovinInterstitialAdManagerProvider, applovinNativeAdManagerProvider, remoteConfigHelperProvider);
  }

  @Override
  public void injectMembers(StartingActivity instance) {
    LocaleAwareActivity_MembersInjector.injectAppRepository(instance, appRepositoryProvider.get());
    injectApplovinInterstitialAdManager(instance, applovinInterstitialAdManagerProvider.get());
    injectApplovinNativeAdManager(instance, applovinNativeAdManagerProvider.get());
    injectRemoteConfigHelper(instance, remoteConfigHelperProvider.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.activity.starting.StartingActivity.applovinInterstitialAdManager")
  public static void injectApplovinInterstitialAdManager(StartingActivity instance,
      ApplovinInterstitialAdManager applovinInterstitialAdManager) {
    instance.applovinInterstitialAdManager = applovinInterstitialAdManager;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.activity.starting.StartingActivity.applovinNativeAdManager")
  public static void injectApplovinNativeAdManager(StartingActivity instance,
      ApplovinNativeAdManager applovinNativeAdManager) {
    instance.applovinNativeAdManager = applovinNativeAdManager;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.activity.starting.StartingActivity.remoteConfigHelper")
  public static void injectRemoteConfigHelper(StartingActivity instance,
      FirebaseRemoteConfigHelper remoteConfigHelper) {
    instance.remoteConfigHelper = remoteConfigHelper;
  }
}
