package com.tqhit.battery.one.features.stats.notifications

import android.app.ActivityManager
import android.app.ForegroundServiceStartNotAllowedException
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import androidx.core.content.ContextCompat
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Helper class for managing the UnifiedBatteryNotificationService.
 * Provides centralized control for starting and stopping the unified notification service.
 */
@Singleton
class UnifiedBatteryNotificationServiceHelper @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val TAG = "UnifiedBatteryNotificationHelper"
        private const val FALLBACK_TAG = "UnifiedBatteryNotificationHelper_Fallback"
    }
    
    /**
     * Checks if the UnifiedBatteryNotificationService is currently running.
     */
    fun isServiceRunning(): Boolean {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val isRunning = activityManager.getRunningServices(Integer.MAX_VALUE)
            .any { it.service.className == UnifiedBatteryNotificationService::class.java.name }
        
        Log.d(TAG, "isServiceRunning: $isRunning")
        return isRunning
    }
    
    /**
     * Starts the UnifiedBatteryNotificationService.
     * Uses foreground service start for Android O+ to comply with background service restrictions.
     * Implements Android 12+ background service restrictions handling with fallback mechanisms.
     */
    fun startService() {
        Log.d(TAG, "Starting UnifiedBatteryNotificationService (Android ${Build.VERSION.SDK_INT})")

        try {
            val intent = Intent(context, UnifiedBatteryNotificationService::class.java)

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                // Use startForegroundService for Android O+ to avoid background service restrictions
                ContextCompat.startForegroundService(context, intent)
                Log.d(TAG, "Started UnifiedBatteryNotificationService as foreground service")
            } else {
                context.startService(intent)
                Log.d(TAG, "Started UnifiedBatteryNotificationService as regular service")
            }
        } catch (e: Exception) {
            handleServiceStartFailure(e)
        }
    }

    /**
     * Handles service startup failures with appropriate fallback mechanisms.
     * Specifically handles Android 12+ ForegroundServiceStartNotAllowedException.
     */
    private fun handleServiceStartFailure(exception: Exception) {
        when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.S &&
            exception is ForegroundServiceStartNotAllowedException -> {
                Log.w(TAG, "ForegroundServiceStartNotAllowedException caught - app likely in background")
                Log.w(TAG, "Android 12+ background service restrictions prevent foreground service startup")
                handleAndroid12PlusRestrictions()
            }
            else -> {
                Log.e(TAG, "Failed to start UnifiedBatteryNotificationService", exception)
                // For other exceptions, try fallback mechanisms
                tryFallbackServiceStart()
            }
        }
    }

    /**
     * Handles Android 12+ specific background service restrictions.
     * Implements fallback strategies when foreground service cannot be started.
     */
    private fun handleAndroid12PlusRestrictions() {
        Log.d(FALLBACK_TAG, "Implementing Android 12+ fallback strategy")

        // Strategy 1: Try to start service without foreground requirement
        tryRegularServiceStart()

        // Strategy 2: Log guidance for proper service startup
        Log.i(FALLBACK_TAG, "Service should be started when app is in foreground")
        Log.i(FALLBACK_TAG, "Consider using WorkManager for background battery monitoring")
    }

    /**
     * Attempts to start the service as a regular service (not foreground).
     * This may work in some cases where foreground service is restricted.
     */
    private fun tryRegularServiceStart() {
        try {
            Log.d(FALLBACK_TAG, "Attempting regular service start as fallback")
            val intent = Intent(context, UnifiedBatteryNotificationService::class.java)
            context.startService(intent)
            Log.d(FALLBACK_TAG, "Successfully started service as regular service")
        } catch (e: Exception) {
            Log.e(FALLBACK_TAG, "Regular service start also failed", e)
            // At this point, we need alternative approaches like WorkManager
            logFallbackGuidance()
        }
    }

    /**
     * Attempts fallback service startup for non-Android 12+ exceptions.
     */
    private fun tryFallbackServiceStart() {
        Log.d(FALLBACK_TAG, "Attempting fallback service start")
        tryRegularServiceStart()
    }

    /**
     * Logs guidance for implementing proper fallback mechanisms.
     */
    private fun logFallbackGuidance() {
        Log.w(FALLBACK_TAG, "All service startup attempts failed")
        Log.w(FALLBACK_TAG, "Consider implementing WorkManager-based battery monitoring")
        Log.w(FALLBACK_TAG, "Core battery monitoring via CoreBatteryStatsService should continue")
    }
    
    /**
     * Stops the UnifiedBatteryNotificationService.
     */
    fun stopService() {
        Log.d(TAG, "Stopping UnifiedBatteryNotificationService")
        
        try {
            val intent = Intent(context, UnifiedBatteryNotificationService::class.java)
            context.stopService(intent)
            Log.d(TAG, "UnifiedBatteryNotificationService stop request sent")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to stop UnifiedBatteryNotificationService", e)
        }
    }
}
