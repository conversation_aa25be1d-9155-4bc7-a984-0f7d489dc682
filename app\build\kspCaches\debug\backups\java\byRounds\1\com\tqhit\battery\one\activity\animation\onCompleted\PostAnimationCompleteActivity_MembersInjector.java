package com.tqhit.battery.one.activity.animation.onCompleted;

import com.tqhit.battery.one.features.navigation.AppNavigator;
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class PostAnimationCompleteActivity_MembersInjector implements MembersInjector<PostAnimationCompleteActivity> {
  private final Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider;

  private final Provider<AppNavigator> appNavigatorProvider;

  public PostAnimationCompleteActivity_MembersInjector(
      Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider,
      Provider<AppNavigator> appNavigatorProvider) {
    this.coreBatteryStatsProvider = coreBatteryStatsProvider;
    this.appNavigatorProvider = appNavigatorProvider;
  }

  public static MembersInjector<PostAnimationCompleteActivity> create(
      Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider,
      Provider<AppNavigator> appNavigatorProvider) {
    return new PostAnimationCompleteActivity_MembersInjector(coreBatteryStatsProvider, appNavigatorProvider);
  }

  @Override
  public void injectMembers(PostAnimationCompleteActivity instance) {
    injectCoreBatteryStatsProvider(instance, coreBatteryStatsProvider.get());
    injectAppNavigator(instance, appNavigatorProvider.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.activity.animation.onCompleted.PostAnimationCompleteActivity.coreBatteryStatsProvider")
  public static void injectCoreBatteryStatsProvider(PostAnimationCompleteActivity instance,
      CoreBatteryStatsProvider coreBatteryStatsProvider) {
    instance.coreBatteryStatsProvider = coreBatteryStatsProvider;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.activity.animation.onCompleted.PostAnimationCompleteActivity.appNavigator")
  public static void injectAppNavigator(PostAnimationCompleteActivity instance,
      AppNavigator appNavigator) {
    instance.appNavigator = appNavigator;
  }
}
