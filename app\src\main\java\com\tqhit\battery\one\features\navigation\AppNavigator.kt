package com.tqhit.battery.one.features.navigation

import android.util.Log
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.LifecycleOwner
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.tqhit.battery.one.R
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment
import com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment
import com.tqhit.battery.one.fragment.main.HealthFragment
import com.tqhit.battery.one.fragment.main.SettingsFragment
import com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment
import com.tqhit.battery.one.fragment.main.others.OthersFragment
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Centralized navigation coordinator following the established stats module architecture pattern.
 * 
 * This class provides a unified interface for all navigation operations in the app,
 * maintaining compatibility with existing CoreBatteryStatsService integration and
 * DynamicNavigationManager functionality while improving testability and maintainability.
 * 
 * Key Responsibilities:
 * - Centralized navigation logic with battery state validation
 * - Intelligent routing through DynamicNavigationManager for charge/discharge fragments
 * - Standard FragmentManager transactions for static fragments
 * - Comprehensive error handling and fallback mechanisms
 * - Debug logging for ADB testing and troubleshooting
 * 
 * Architecture Compliance:
 * - Follows SOLID principles with single responsibility for navigation coordination
 * - Maintains stats module architecture pattern consistency
 * - Proper dependency injection with Hilt
 * - MVI pattern compatibility for state management
 * 
 * @param dynamicNavigationManager Handles battery state-based navigation for charge/discharge
 * @param coreBatteryStatsProvider Provides current battery status for navigation decisions
 */
@Singleton
class AppNavigator @Inject constructor(
    private val dynamicNavigationManager: DynamicNavigationManager,
    private val coreBatteryStatsProvider: CoreBatteryStatsProvider
) {
    companion object {
        private const val TAG = "AppNavigator"
        
        // Navigation result codes
        const val NAVIGATION_SUCCESS = true
        const val NAVIGATION_FAILED = false
        
        // Fragment tags for back stack management
        private const val FRAGMENT_TAG_PREFIX = "app_nav_"
    }
    
    // Navigation state tracking
    private var isInitialized = false
    private var fragmentManager: FragmentManager? = null
    private var bottomNavigationView: BottomNavigationView? = null
    private var fragmentContainerId: Int = 0
    private var lifecycleOwner: LifecycleOwner? = null
    
    // Performance tracking
    private var navigationCount = 0
    private var successfulNavigations = 0
    private var failedNavigations = 0
    
    /**
     * Initializes the AppNavigator with required dependencies.
     * Must be called before any navigation operations.
     * 
     * @param fragmentManager The FragmentManager for fragment transactions
     * @param bottomNavigationView The BottomNavigationView to manage
     * @param fragmentContainerId The container ID for fragment replacement
     * @param lifecycleOwner The lifecycle owner for coroutine scope
     */
    fun initialize(
        fragmentManager: FragmentManager,
        bottomNavigationView: BottomNavigationView,
        fragmentContainerId: Int,
        lifecycleOwner: LifecycleOwner
    ) {
        Log.d(TAG, "NAVIGATOR_INIT: Initializing AppNavigator")
        
        this.fragmentManager = fragmentManager
        this.bottomNavigationView = bottomNavigationView
        this.fragmentContainerId = fragmentContainerId
        this.lifecycleOwner = lifecycleOwner
        this.isInitialized = true
        
        Log.d(TAG, "NAVIGATOR_INIT: AppNavigator initialized successfully")
        Log.d(TAG, "NAVIGATOR_INIT: FragmentManager: ${fragmentManager.javaClass.simpleName}")
        Log.d(TAG, "NAVIGATOR_INIT: Container ID: $fragmentContainerId")
        Log.d(TAG, "NAVIGATOR_INIT: LifecycleOwner: ${lifecycleOwner.javaClass.simpleName}")
    }
    
    /**
     * Navigates to the appropriate charge fragment based on current battery state.
     * Uses DynamicNavigationManager for intelligent battery state-based routing.
     * 
     * @return Boolean indicating navigation success
     */
    fun navigateToCharge(): Boolean {
        Log.d(TAG, "NAVIGATION_REQUEST: Navigate to charge fragment")
        
        if (!validateInitialization()) {
            return NAVIGATION_FAILED
        }
        
        return performNavigation(
            targetFragmentId = R.id.chargeFragment,
            navigationMethod = NavigationMethod.DYNAMIC,
            reason = "User requested charge navigation"
        )
    }
    
    /**
     * Navigates to the appropriate discharge fragment based on current battery state.
     * Uses DynamicNavigationManager for intelligent battery state-based routing.
     * 
     * @return Boolean indicating navigation success
     */
    fun navigateToDischarge(): Boolean {
        Log.d(TAG, "NAVIGATION_REQUEST: Navigate to discharge fragment")
        
        if (!validateInitialization()) {
            return NAVIGATION_FAILED
        }
        
        return performNavigation(
            targetFragmentId = R.id.dischargeFragment,
            navigationMethod = NavigationMethod.DYNAMIC,
            reason = "User requested discharge navigation"
        )
    }
    
    /**
     * Navigates to the health fragment using standard fragment transactions.
     * 
     * @return Boolean indicating navigation success
     */
    fun navigateToHealth(): Boolean {
        Log.d(TAG, "NAVIGATION_REQUEST: Navigate to health fragment")
        
        if (!validateInitialization()) {
            return NAVIGATION_FAILED
        }
        
        return performNavigation(
            targetFragmentId = R.id.healthFragment,
            navigationMethod = NavigationMethod.STANDARD,
            reason = "User requested health navigation"
        )
    }
    
    /**
     * Navigates to the others fragment using standard fragment transactions.
     * 
     * @return Boolean indicating navigation success
     */
    fun navigateToOthers(): Boolean {
        Log.d(TAG, "NAVIGATION_REQUEST: Navigate to others fragment")
        
        if (!validateInitialization()) {
            return NAVIGATION_FAILED
        }
        
        return performNavigation(
            targetFragmentId = R.id.othersFragment,
            navigationMethod = NavigationMethod.STANDARD,
            reason = "User requested others navigation"
        )
    }
    
    /**
     * Navigates to the animation fragment using standard fragment transactions.
     * 
     * @return Boolean indicating navigation success
     */
    fun navigateToAnimation(): Boolean {
        Log.d(TAG, "NAVIGATION_REQUEST: Navigate to animation fragment")
        
        if (!validateInitialization()) {
            return NAVIGATION_FAILED
        }
        
        return performNavigation(
            targetFragmentId = R.id.animationGridFragment,
            navigationMethod = NavigationMethod.STANDARD,
            reason = "User requested animation navigation"
        )
    }
    
    /**
     * Navigates to the settings fragment using standard fragment transactions.
     * 
     * @return Boolean indicating navigation success
     */
    fun navigateToSettings(): Boolean {
        Log.d(TAG, "NAVIGATION_REQUEST: Navigate to settings fragment")
        
        if (!validateInitialization()) {
            return NAVIGATION_FAILED
        }
        
        return performNavigation(
            targetFragmentId = R.id.settingsFragment,
            navigationMethod = NavigationMethod.STANDARD,
            reason = "User requested settings navigation"
        )
    }
    
    /**
     * Handles back navigation using Navigation Component back stack management.
     * 
     * @return Boolean indicating whether back navigation was handled
     */
    fun navigateBack(): Boolean {
        Log.d(TAG, "NAVIGATION_REQUEST: Navigate back")
        
        if (!validateInitialization()) {
            return NAVIGATION_FAILED
        }
        
        val fragmentManager = this.fragmentManager ?: return NAVIGATION_FAILED
        
        return try {
            if (fragmentManager.backStackEntryCount > 0) {
                Log.d(TAG, "BACK_NAVIGATION: Popping back stack (entries: ${fragmentManager.backStackEntryCount})")
                fragmentManager.popBackStack()
                Log.d(TAG, "BACK_NAVIGATION: Back navigation successful")
                NAVIGATION_SUCCESS
            } else {
                Log.d(TAG, "BACK_NAVIGATION: No back stack entries, cannot navigate back")
                NAVIGATION_FAILED
            }
        } catch (e: Exception) {
            Log.e(TAG, "BACK_NAVIGATION: Error during back navigation", e)
            NAVIGATION_FAILED
        }
    }
    
    /**
     * Gets current navigation performance statistics.
     * 
     * @return String containing performance metrics
     */
    fun getPerformanceStats(): String {
        return buildString {
            appendLine("AppNavigator Performance Stats:")
            appendLine("  Total Navigations: $navigationCount")
            appendLine("  Successful: $successfulNavigations")
            appendLine("  Failed: $failedNavigations")
            appendLine("  Success Rate: ${if (navigationCount > 0) (successfulNavigations * 100 / navigationCount) else 0}%")
            appendLine("  Initialized: $isInitialized")
        }
    }
    
    /**
     * Checks if the AppNavigator is properly initialized.
     * 
     * @return Boolean indicating initialization status
     */
    fun isInitialized(): Boolean = isInitialized
    
    /**
     * Validates that the AppNavigator is properly initialized before navigation.
     *
     * @return Boolean indicating whether navigation can proceed
     */
    private fun validateInitialization(): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "NAVIGATION_ERROR: AppNavigator not initialized - call initialize() first")
            return false
        }

        if (fragmentManager == null) {
            Log.e(TAG, "NAVIGATION_ERROR: FragmentManager is null")
            return false
        }

        if (bottomNavigationView == null) {
            Log.e(TAG, "NAVIGATION_ERROR: BottomNavigationView is null")
            return false
        }

        return true
    }

    /**
     * Performs the actual navigation based on the specified method and target.
     *
     * @param targetFragmentId The resource ID of the target fragment
     * @param navigationMethod The navigation method to use (DYNAMIC or STANDARD)
     * @param reason Human-readable reason for the navigation
     * @return Boolean indicating navigation success
     */
    private fun performNavigation(
        targetFragmentId: Int,
        navigationMethod: NavigationMethod,
        reason: String
    ): Boolean {
        val startTime = System.currentTimeMillis()
        navigationCount++

        Log.d(TAG, "NAVIGATION_PERFORM: Starting navigation to ${getFragmentName(targetFragmentId)}")
        Log.d(TAG, "NAVIGATION_PERFORM: Method: $navigationMethod, Reason: $reason")

        val result = try {
            when (navigationMethod) {
                NavigationMethod.DYNAMIC -> performDynamicNavigation(targetFragmentId, reason)
                NavigationMethod.STANDARD -> performStandardNavigation(targetFragmentId, reason)
            }
        } catch (e: Exception) {
            Log.e(TAG, "NAVIGATION_ERROR: Exception during navigation", e)
            false
        }

        val navigationTime = System.currentTimeMillis() - startTime

        if (result) {
            successfulNavigations++
            Log.d(TAG, "NAVIGATION_SUCCESS: Navigation completed in ${navigationTime}ms")
        } else {
            failedNavigations++
            Log.e(TAG, "NAVIGATION_FAILED: Navigation failed after ${navigationTime}ms")
        }

        return result
    }

    /**
     * Performs dynamic navigation using DynamicNavigationManager.
     * Used for charge/discharge fragments that require battery state validation.
     *
     * @param targetFragmentId The resource ID of the target fragment
     * @param reason Human-readable reason for the navigation
     * @return Boolean indicating navigation success
     */
    private fun performDynamicNavigation(targetFragmentId: Int, reason: String): Boolean {
        Log.d(TAG, "DYNAMIC_NAVIGATION: Using DynamicNavigationManager for ${getFragmentName(targetFragmentId)}")

        // Validate battery state for charge/discharge navigation
        val currentBatteryStatus = coreBatteryStatsProvider.getCurrentStatus()
        val actualIsCharging = currentBatteryStatus?.isCharging ?: false
        val shouldBeCharging = targetFragmentId == R.id.chargeFragment

        Log.d(TAG, "DYNAMIC_NAVIGATION: Battery state validation:")
        Log.d(TAG, "DYNAMIC_NAVIGATION:   - Target should be charging: $shouldBeCharging")
        Log.d(TAG, "DYNAMIC_NAVIGATION:   - Actual battery charging: $actualIsCharging")
        Log.d(TAG, "DYNAMIC_NAVIGATION:   - Battery status available: ${currentBatteryStatus != null}")

        // Use DynamicNavigationManager for intelligent routing
        val navigationSuccess = dynamicNavigationManager.handleUserNavigation(targetFragmentId)

        if (navigationSuccess) {
            Log.d(TAG, "DYNAMIC_NAVIGATION: DynamicNavigationManager navigation successful")
            updateBottomNavigation(targetFragmentId)
        } else {
            Log.w(TAG, "DYNAMIC_NAVIGATION: DynamicNavigationManager failed, attempting fallback")
            return performFallbackNavigation(targetFragmentId, reason)
        }

        return navigationSuccess
    }

    /**
     * Performs standard navigation using FragmentManager transactions.
     * Used for static fragments that don't require battery state validation.
     *
     * @param targetFragmentId The resource ID of the target fragment
     * @param reason Human-readable reason for the navigation
     * @return Boolean indicating navigation success
     */
    private fun performStandardNavigation(targetFragmentId: Int, reason: String): Boolean {
        Log.d(TAG, "STANDARD_NAVIGATION: Using FragmentManager for ${getFragmentName(targetFragmentId)}")

        val fragmentManager = this.fragmentManager ?: return false
        val fragment = createFragment(targetFragmentId) ?: return false

        return try {
            fragmentManager
                .beginTransaction()
                .replace(fragmentContainerId, fragment, "$FRAGMENT_TAG_PREFIX${getFragmentName(targetFragmentId)}")
                .addToBackStack(null)
                .commit()
//
//            updateBottomNavigation(targetFragmentId)
            Log.d(TAG, "STANDARD_NAVIGATION: Fragment transaction committed successfully")
            true
        } catch (e: Exception) {
            Log.e(TAG, "STANDARD_NAVIGATION: Error during fragment transaction", e)
            false
        }
    }

    /**
     * Performs fallback navigation when primary navigation methods fail.
     *
     * @param targetFragmentId The resource ID of the target fragment
     * @param reason Human-readable reason for the navigation
     * @return Boolean indicating navigation success
     */
    private fun performFallbackNavigation(targetFragmentId: Int, reason: String): Boolean {
        Log.w(TAG, "FALLBACK_NAVIGATION: Attempting fallback for ${getFragmentName(targetFragmentId)}")

        val fragmentManager = this.fragmentManager ?: return false
        val fragment = createFragment(targetFragmentId) ?: return false

        return try {
            fragmentManager
                .beginTransaction()
                .replace(fragmentContainerId, fragment)
                .addToBackStack(null)
                .commit()

            updateBottomNavigation(targetFragmentId)
            Log.d(TAG, "FALLBACK_NAVIGATION: Fallback navigation successful")
            true
        } catch (e: Exception) {
            Log.e(TAG, "FALLBACK_NAVIGATION: Fallback navigation also failed", e)
            false
        }
    }

    /**
     * Creates a fragment instance based on the fragment ID.
     *
     * @param fragmentId The resource ID of the fragment to create
     * @return Fragment instance or null if unknown fragment ID
     */
    private fun createFragment(fragmentId: Int): Fragment? {
        return when (fragmentId) {
            R.id.chargeFragment -> StatsChargeFragment()
            R.id.dischargeFragment -> DischargeFragment()
            R.id.healthFragment -> HealthFragment()
            R.id.settingsFragment -> SettingsFragment()
            R.id.animationGridFragment -> AnimationGridFragment()
            R.id.othersFragment -> OthersFragment()
            else -> {
                Log.e(TAG, "FRAGMENT_CREATION: Unknown fragment ID: $fragmentId")
                null
            }
        }
    }

    /**
     * Updates the bottom navigation view to reflect the current fragment.
     *
     * @param fragmentId The resource ID of the active fragment
     */
    private fun updateBottomNavigation(fragmentId: Int) {
        val bottomNav = this.bottomNavigationView ?: return

        try {
            bottomNav.selectedItemId = fragmentId
            Log.d(TAG, "BOTTOM_NAV: Updated selection to ${getFragmentName(fragmentId)}")
        } catch (e: Exception) {
            Log.e(TAG, "BOTTOM_NAV: Error updating bottom navigation", e)
        }
    }

    /**
     * Gets a human-readable fragment name for logging.
     *
     * @param fragmentId The resource ID of the fragment
     * @return String name of the fragment
     */
    private fun getFragmentName(fragmentId: Int): String {
        return when (fragmentId) {
            R.id.chargeFragment -> "ChargeFragment"
            R.id.dischargeFragment -> "DischargeFragment"
            R.id.healthFragment -> "HealthFragment"
            R.id.settingsFragment -> "SettingsFragment"
            R.id.animationGridFragment -> "AnimationFragment"
            R.id.othersFragment -> "OthersFragment"
            else -> "UnknownFragment($fragmentId)"
        }
    }

    /**
     * Navigation method types for internal routing logic.
     */
    private enum class NavigationMethod {
        DYNAMIC,    // Use DynamicNavigationManager for charge/discharge
        STANDARD    // Use standard FragmentManager transactions
    }
}
