@echo off
echo ========================================
echo TJ_BatteryOne Emoji Touch Fix Test
echo ========================================
echo.

echo Building and installing debug APK...
call .\gradlew assembleDebug
if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo Installing APK...
adb install -r app\build\outputs\apk\debug\app-debug.apk
if %ERRORLEVEL% neq 0 (
    echo Install failed!
    pause
    exit /b 1
)

echo.
echo Starting application...
adb shell am start -W -n com.fc.p.tj.charginganimation.batterycharging.chargeeffect/com.tqhit.battery.one.activity.splash.SplashActivity

echo.
echo ========================================
echo MANUAL TESTING INSTRUCTIONS
echo ========================================
echo.
echo 1. Wait for the app to fully load
echo 2. Navigate to the Emoji section (usually bottom navigation)
echo 3. Find and tap on any emoji style to open the customize activity
echo 4. In the customize activity, test tapping on carousel items:
echo    - Test positions 1, 2, 3 (should work for both free and premium)
echo    - Test position 4 (previously broken - should now work)
echo    - Test positions 5, 6, 7 (should work for both free and premium)
echo    - Test position 8 (previously broken - should now work)
echo    - Continue testing every 4th position (12, 16, etc.)
echo.
echo 5. Test both Battery Carousel and Emoji Carousel
echo 6. PREMIUM ITEMS: Verify premium items show lock icon but still update preview
echo 7. Look for TOUCH_DEBUG and PREMIUM_PREVIEW_FIX logs in the console below
echo.
echo ========================================
echo LOGCAT MONITORING (Touch Events)
echo ========================================
echo Press Ctrl+C to stop monitoring
echo.

REM Clear logcat buffer
adb logcat -c

REM Start comprehensive logcat monitoring
adb logcat -s TOUCH_DEBUG:D BATTERY_COMPONENT_ADAPTER:D EMOJI_COMPONENT_ADAPTER:D EMOJI_CUSTOMIZE_ACTIVITY:D EmojiCustomizeActivity:D BatteryComponentAdapter:D EmojiComponentAdapter:D PREMIUM_PREVIEW_FIX:D

pause
