package com.tqhit.battery.one.ads.core

import android.os.Handler
import android.os.Looper
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxAdListener
import com.applovin.mediation.MaxAdRevenueListener
import com.applovin.mediation.ads.MaxAppOpenAd
import com.applovin.mediation.MaxError
import com.google.firebase.analytics.FirebaseAnalytics
import com.ironsource.pl
import com.tqhit.adlib.sdk.analytics.AnalyticsTracker
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ApplovinAppOpenAdManager @Inject constructor(
    private val remoteConfigHelper: FirebaseRemoteConfigHelper,
    private val analyticsTracker: AnalyticsTracker,
) : MaxAdListener, MaxAdRevenueListener {
    private val adUnitId = "65e6e86ac6a58121"
    private val waitTime = 1000L
    private val maxRetryCount = 6

    private var retryCount = 0
    private var appOpenAd: MaxAppOpenAd? = null
    private var enable: Boolean = true
    private val handler = Handler(Looper.getMainLooper())
    private var lastShowTime: Long = 0

    fun setEnabled(value: Boolean) {
        enable = value
    }

    fun loadAppOpenAd() {
        if (!enable || appOpenAd?.isReady == true) return
        appOpenAd = MaxAppOpenAd(adUnitId)
        appOpenAd?.setListener(this)
        appOpenAd?.setRevenueListener(this)
        appOpenAd?.loadAd()
        analyticsTracker.logEvent("aoa_load")
    }

    fun showAppOpenAd(
        placementName: String? = null,
    ) {
        if (!enable) return
        analyticsTracker.logEvent("aoa_show", mapOf("placement" to (placementName ?: "default")))
        if (appOpenAd?.isReady == true) {
            val aoaShowFrequency = remoteConfigHelper.getLong("aoa_show_frequency").toInt()
            if (System.currentTimeMillis() - lastShowTime < aoaShowFrequency * 1000L) return
            if (placementName != null) {
                appOpenAd?.showAd(placementName)
            } else {
                appOpenAd?.showAd()
            }
        } else {
            loadAppOpenAd()
        }
    }

    override fun onAdLoaded(ad: MaxAd) {
        analyticsTracker.logEvent("aoa_load_success", mapOf("placement" to ad.placement))
    }

    override fun onAdLoadFailed(adUnitId: String, error: MaxError) {
        analyticsTracker.logEvent("aoa_load_fail")
        destroy()
        val retryDelay = calculateWaitTime(retryCount)
        if (retryDelay < 0) return
        handler.postDelayed({ loadAppOpenAd() }, retryDelay)
        retryCount++
    }

    override fun onAdDisplayFailed(ad: MaxAd, error: MaxError) {
        destroy()
        loadAppOpenAd()
        analyticsTracker.logEvent("aoa_show_fail", mapOf("placement" to ad.placement))
    }

    override fun onAdDisplayed(ad: MaxAd) {
        analyticsTracker.logEvent("aoa_show_success", mapOf("placement" to ad.placement))
    }

    override fun onAdHidden(ad: MaxAd) {
        lastShowTime = System.currentTimeMillis()
        destroy()
        loadAppOpenAd()
        analyticsTracker.logEvent("aoa_close", mapOf("placement" to ad.placement))
    }

    override fun onAdClicked(ad: MaxAd) {
        analyticsTracker.logEvent("aoa_click", mapOf("placement" to ad.placement))
    }

    private fun destroy() {
        appOpenAd?.destroy()
        appOpenAd = null
    }

    override fun onAdRevenuePaid(impressionData: MaxAd) {
        impressionData.let {
            analyticsTracker.logEvent(
                FirebaseAnalytics.Event.AD_IMPRESSION,
                mapOf(
                    FirebaseAnalytics.Param.AD_PLATFORM to "appLovin",
                    FirebaseAnalytics.Param.AD_UNIT_NAME to impressionData.adUnitId,
                    FirebaseAnalytics.Param.AD_FORMAT to impressionData.format.label,
                    FirebaseAnalytics.Param.AD_SOURCE to impressionData.networkName,
                    FirebaseAnalytics.Param.VALUE to impressionData.revenue,
                    FirebaseAnalytics.Param.CURRENCY to "USD",
                    "placement" to impressionData.placement,
                )
            )
            analyticsTracker.logEvent(
                "ad_impression_custom",
                mapOf(
                    FirebaseAnalytics.Param.AD_PLATFORM to "appLovin",
                    FirebaseAnalytics.Param.AD_UNIT_NAME to impressionData.adUnitId,
                    FirebaseAnalytics.Param.AD_FORMAT to impressionData.format.label,
                    FirebaseAnalytics.Param.AD_SOURCE to impressionData.networkName,
                    FirebaseAnalytics.Param.VALUE to impressionData.revenue,
                    FirebaseAnalytics.Param.CURRENCY to "USD",
                    "placement" to impressionData.placement,
                    "impression_count" to 1,
                )
            )
        }
    }

    private fun calculateWaitTime(
        retryCount: Int
    ): Long {
        if (retryCount < 0 || retryCount > maxRetryCount) {
            return -1L
        }
        return waitTime * (1 shl retryCount)
    }
}