## Codebase Summary: Battery Charging Animation 3D
Bundle id: com.fc.p.tj.charginganimation.batterycharging.chargeeffect

### 1. Overall Purpose

The application is a comprehensive battery utility designed to provide users with detailed statistics about their device's battery charging and discharging cycles, health information, and various customization options. It features distinct, modern modules for tracking both charging (`features.charge`) and discharging (`features.new_discharge`) behavior with greater accuracy. Aesthetic enhancements like charging animations, security features like anti-theft protection, and configurable battery alarms are also key components. The app aims to empower users with a better understanding and control over their device's battery performance and longevity.

### 2. Key Features

*   **Battery Statistics (Charging & Discharging):**
    *   **New Charge Feature (`features.charge` package):**
        *   Real-time monitoring of battery status (percentage, charging state, current, voltage, temperature) via `ChargeDataRepository` and `AndroidBatteryStatsDataSource`.
        *   Manages charge sessions (`NewChargeSession`) through `DefaultManageChargeSessionUseCase` and `PrefsChargeSessionRepository`, tracking start/end times, percentages, estimated mAh charged, screen on/off times, and average charge rates (mAh/h, %/h).
        *   Calculates estimated time to full charge and time to a user-defined target percentage using `CalculateChargeEstimatesUseCase`, considering current session data.
        *   Provides a `NewChargeFragment` and `NewChargeViewModel` for displaying this data.
        *   Includes `NewChargeMonitorService` (managed by `ChargeMonitorServiceHelper`) for background charge monitoring and related notifications.
    *   **New Discharge Feature (`features.new_discharge` package):**
        *   Robust and detailed tracking of discharge sessions (`NewDischargeSessionData`).
        *   Calculates mAh-based consumption for screen-on and screen-off states.
        *   Offers improved time estimations (screen-on, screen-off, mixed usage) based on learned discharge rates (`NewBatteryRepository` learns these) and current battery capacity.
        *   Handles gap estimation (`GapEstimationCalculator` & `FullSessionReEstimator`) for sessions when the app restarts, distributing consumption across screen on/off states.
        *   Manages current session data with detailed metrics like duration, start/current percentage, total mAh consumed, and various average discharge rates (`SessionManager`, `SessionMetricsCalculator`).
        *   UI provided by `NewDischargeFragment` and `NewDischargeViewModel`.
    *   **Legacy Components (Still Present but Largely Superseded):**
        *   `ChargeFragment` and `DischargeFragment` (old implementations).
        *   `BatteryRepository` (legacy) for general battery info, historical data, and old session management.
        *   `ChargingSessionManager` & `DischargeSessionManager` (legacy session managers).
    *   Historical data tracking for battery percentage and temperature (`BatteryHistoryManager`, `TemperatureHistoryManager`), displayed in `HealthFragment`.

*   **Battery Health:**
    *   Calculated battery health percentage.
    *   Estimated battery wear in cycles.
    *   Display of design vs. user-configurable battery capacity (`AppRepository`).
    *   Daily battery wear tracking and visualization (`HealthFragment`).

*   **Charging Animations:**
    *   Selection and application of video animations during charging (`AnimationActivity`, `ChargingOverlayActivity`).
    *   Animations sourced from Firebase Remote Config (`animation_json`), managed by `AnimationRepository` and `AnimationViewModel`.
    *   `ExoPlayer` for video playback.
    *   `ChargingOverlayService` for lock screen overlay.
    *   Option to show/hide date and time on animation overlay.

*   **Customization:**
    *   Theme selection (Light, Dark, Amoled, Grey, Auto, and inverted versions) via `ThemeManager`.
    *   Accent color selection.
    *   Language selection, dynamically updating UI via `AppRepository`.

*   **Alarms & Notifications:**
    *   **Charge Alarms (managed by `NewChargeMonitorService` & `CheckChargeAlarmUseCase`):**
        *   Target charge percentage reached.
        *   Battery fully charged.
        *   Charging started/stopped notifications.
    *   **Discharge Alarms (managed by legacy `BatteryMonitorService`):**
        *   Low battery alarm.
    *   Do Not Disturb mode for alarms (time-based).
    *   Optional vibration for alarms and state changes (`VibrationService`).
    *   Persistent notification displaying key battery metrics (from both `NewChargeMonitorService` and legacy `BatteryMonitorService`).

*   **Anti-Theft Protection (handled by `NewChargeMonitorService`):**
    *   Alerts if charger is disconnected while active.
    *   Requires user-set password (`SetupPasswordDialog`, `EnterPasswordActivity`) to disable.
    *   Optional warning sound.

*   **Onboarding & Permissions (`StartingActivity`):**
    *   Guides new users through privacy policy, notification permission, and battery optimization.
    *   Initial charge alarm setup.

*   **Monetization (Inferred):**
    *   AppLovin Ad SDK integration (Banner, Interstitial, Rewarded, App Open via `Applovin...AdManager` classes).
        *   *Note: Core ad-showing logic within AdManager classes is currently commented out.*
    *   Firebase Remote Config for ad behavior and feature flags.
    *   Premium animations and 24-hour trials.

*   **Development & Debugging:**
    *   `DebugActivity` (launches test activities).
    *   `TestNewDischargeActivity` for the new discharge module.
    *   `TestNewChargeActivity` for the new charge module and its service.

### 3. Project Structure

The project is organized into a main application package `com.tqhit.battery.one` with sub-packages for activities, fragments, services, repositories, viewmodels, dialogs, and new feature modules (`features.charge`, `features.new_discharge`).

```
├── java/com/tqhit/battery/one/
│   ├── activity/
│   ├── ads/core/
│   ├── component/progress/
│   ├── dialog/
│   ├── features/                    # NEW: Dedicated feature modules
│   │   ├── charge/                  # NEW: Charging feature
│   │   │   ├── common/
│   │   │   ├── data/model/
│   │   │   ├── data/repository/
│   │   │   ├── datasource/
│   │   │   ├── di/
│   │   │   ├── domain/
│   │   │   ├── presentation/
│   │   │   └── service/
│   │   └── new_discharge/           # NEW: Discharge feature
│   │       ├── cache/
│   │       ├── data/
│   │       ├── datasource/
│   │       ├── di/
│   │       ├── domain/
│   │       ├── presentation/
│   │       └── repository/
│   ├── fragment/main/
│   ├── manager/                     # Legacy managers
│   ├── repository/                  # Legacy/General repositories
│   ├── service/                     # General/Legacy services
│   ├── utils/
│   ├── viewmodel/                   # Legacy/General ViewModels
│   └── BatteryApplication.kt
├── res/
│   ├── layout/                      # Includes new section layouts for charge/discharge
...
```

### 4. File Breakdown and Summaries

#### 4.1. Core Application & Setup

*   **`BatteryApplication.kt`**: Initializes Firebase, AppLovin SDK, `ThemeManager`, language. Manages app session count.
*   **`AndroidManifest.xml`**: Declares components, permissions. Launcher is `SplashActivity`. Includes `NewChargeMonitorService`.

#### 4.2. Activities

*   **`SplashActivity.kt`**: Navigates to `StartingActivity` or `MainActivity`.
*   **`StartingActivity.kt`**: Onboarding for privacy, permissions, initial alarm setup.
*   **`MainActivity.kt`**: Main UI host with `BottomNavigationView`.
    *   **Initial Fragment Logic:** Now correctly loads `NewChargeFragment` or `NewDischargeFragment` based on charging state.
    *   Starts `BatteryMonitorService` (legacy), `NewChargeMonitorService` (new), and `ChargingOverlayService`.
*   **`AnimationActivity.kt`**: Displays charging animation with `ExoPlayer`. Handles trial logic.
*   **`ChargingOverlayActivity.kt`**: Lock screen overlay for animations.
*   **`EnterPasswordActivity.kt`**: Password prompt for anti-theft.
*   **`DebugActivity.kt`**: Launches test activities.
*   **`TestNewChargeActivity.kt`**: For testing `NewChargeFragment` and `ChargeMonitorServiceHelper`.
*   **`TestNewDischargeActivity.kt`**: For testing `NewDischargeFragment`.

#### 4.3. Fragments

*   **`NewChargeFragment.kt` (`features.charge.presentation`)**: **Primary fragment for charging information.**
    *   Uses `NewChargeViewModel` to display data from the new charging feature module.
    *   UI includes sections for main display, battery wear (target %), status details, remaining time, current session, and overall averages.
*   **`NewDischargeFragment.kt` (`features.new_discharge.presentation`)**: **Primary fragment for discharge information.**
    *   Uses `NewDischargeViewModel` to display detailed discharge stats, mAh consumption, learned rate-based time estimates.
*   **`HealthFragment.kt`**: Displays battery health, historical charts for percentage/temperature, daily wear.
*   **`SettingsFragment.kt`**: UI for app settings (theme, language, notifications, animation, anti-theft, etc.).
*   **`AnimationGridFragment.kt`**: Grid of charging animations from Firebase.
*   **`ChargeFragment.kt` & `DischargeFragment.kt` (legacy)**: Still present but `MainActivity` defaults to the "New" versions.

#### 4.4. ViewModels

*   **`AppViewModel.kt`**: Manages app-wide settings via `AppRepository`.
*   **`AnimationViewModel.kt`**: Manages animation states via `AnimationRepository`.
*   **`NewChargeViewModel.kt` (`features.charge.presentation`)**:
    *   For `NewChargeFragment`.
    *   Collects data from `ChargeDataRepository`, `ManageChargeSessionUseCase`, and `AppRepository` (for target percentage).
    *   Uses `CalculateChargeEstimatesUseCase` to get real-time charge estimates.
    *   Exposes `ChargeUiState`. Handles session reset via `ManageChargeSessionUseCase`.
*   **`NewDischargeViewModel.kt` (`features.new_discharge.presentation`)**:
    *   For `NewDischargeFragment`. Observes `NewBatteryRepository` and `NewDischargeSessionRepository`.
    *   Uses `NewDischargeCalculator` and `TimeConverter`. Exposes `NewDischargeUiState`.
*   **`BatteryViewModel.kt` (legacy)**: Used by old `ChargeFragment` and `DischargeFragment`.

#### 4.5. Repositories & Data Sources

*   **`AppRepository.kt`**: Manages app settings preferences (privacy, alarms, language, anti-theft, animation path, battery capacity).
*   **`AnimationRepository.kt`**: Manages animation trial status and applied animation preferences.
*   **`BatteryRepository.kt` (legacy/general)**: Provides general battery info (health, historical data). Legacy session management is largely superseded.
*   **`features.charge` module:**
    *   **`AndroidBatteryStatsDataSource.kt`**: Provides `Flow<NewChargeStatus>` from system battery broadcasts.
    *   **`ChargeDataRepository.kt`**: Consumes `NewChargeStatus` flow, makes it available.
    *   **`PrefsChargeSessionRepository.kt`**: SharedPreferences implementation for storing `NewChargeSession` data (active, history, latest completed).
*   **`features.new_discharge` module:**
    *   **`AndroidBatteryDataSource.kt`**: Provides `Flow<NewBatteryStatus>` from system broadcasts.
    *   **`PrefsBatteryStatusCache.kt`, `PrefsCurrentSessionCache.kt`, `PrefsDischargeRatesCache.kt`**: Cache implementations.
    *   **`NewBatteryRepository.kt`**: Provides `NewBatteryStatus`, learns and caches average discharge rates, determines effective capacity.
    *   **`NewDischargeSessionRepository.kt`**: Manages `NewDischargeSessionData` lifecycle, processes status updates, handles gap estimation.

#### 4.6. Domain Logic / Use Cases

*   **`features.charge` module:**
    *   **`CalculateChargeEstimatesUseCase.kt`**: Calculates `ChargeEstimates` (time to full/target, rates) based on `NewChargeStatus`, `NewChargeSession`, target percentage, and historical rates.
    *   **`DefaultManageChargeSessionUseCase.kt`**: Manages `NewChargeSession` lifecycle (start, update, end, reset). Tracks screen on/off time during charging and calculates various session metrics.
    *   **`DefaultCheckChargeAlarmUseCase.kt`**: Logic for triggering charge-related alarms (target reached, full charge, charging state change, anti-theft) considering DND settings.
*   **`features.new_discharge` module (Domain):**
    *   `DischargeRateCalculator.kt`, `FullSessionReEstimator.kt`, `GapEstimationCalculator.kt`, `NewDischargeCalculator.kt`, `ScreenTimeCalculator.kt`, `SessionManager.kt`, `SessionMetricsCalculator.kt`, `TimeConverter.kt`: Core calculators and managers for the detailed discharge tracking.

#### 4.7. Services

*   **`NewChargeMonitorService.kt` (`features.charge.service`)**: **Primary service for charging.**
    *   Monitors `NewChargeStatus` via `ChargeDataRepository`.
    *   Uses `ChargeNotificationManager` to display/update a persistent service notification with current metrics.
    *   Uses `CheckChargeAlarmUseCase` to trigger notifications for target charge reached, full charge, charging started/stopped.
    *   Handles anti-theft logic (launching `EnterPasswordActivity`).
*   **`ChargeMonitorServiceHelper.kt` (`features.charge.service`)**: Helper to start/stop/check `NewChargeMonitorService`.
*   **`BatteryMonitorService.kt` (legacy)**: Older service for general battery monitoring and alarms.
*   **`ChargingOverlayService.kt`**: Manages the charging animation overlay.
*   **`VibrationService.kt`**: Provides haptic feedback.

#### 4.8. Ad Management (`ads/core/`)

*   Managers for AppLovin ad formats. Core ad display logic is currently commented out.

#### 4.9. Utilities (`utils/`)

*   `AntiThiefUtils.kt`, `BatteryUtils.kt` (legacy), `DateTimeUtils.kt`, `NotificationUtils.kt`, `PermissionUtils.kt`, `VideoUtils.kt`.

#### 4.10. Resources

*   Layouts now include modular sections for the new charge (`section_charge_*.xml`) and discharge (`layout_discharge_section_*.xml`) screens.
*   `nav_graph.xml` correctly routes to `NewChargeFragment` and `NewDischargeFragment`.
*   Extensive localized strings and theme-aware drawables.