package com.tqhit.battery.one.activity.splash;

import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager;
import com.tqhit.battery.one.initialization.InitializationProgressManager;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class SplashActivity_MembersInjector implements MembersInjector<SplashActivity> {
  private final Provider<InitializationProgressManager> initializationProgressManagerProvider;

  private final Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider;

  public SplashActivity_MembersInjector(
      Provider<InitializationProgressManager> initializationProgressManagerProvider,
      Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider) {
    this.initializationProgressManagerProvider = initializationProgressManagerProvider;
    this.applovinInterstitialAdManagerProvider = applovinInterstitialAdManagerProvider;
  }

  public static MembersInjector<SplashActivity> create(
      Provider<InitializationProgressManager> initializationProgressManagerProvider,
      Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider) {
    return new SplashActivity_MembersInjector(initializationProgressManagerProvider, applovinInterstitialAdManagerProvider);
  }

  @Override
  public void injectMembers(SplashActivity instance) {
    injectInitializationProgressManager(instance, initializationProgressManagerProvider.get());
    injectApplovinInterstitialAdManager(instance, applovinInterstitialAdManagerProvider.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.activity.splash.SplashActivity.initializationProgressManager")
  public static void injectInitializationProgressManager(SplashActivity instance,
      InitializationProgressManager initializationProgressManager) {
    instance.initializationProgressManager = initializationProgressManager;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.activity.splash.SplashActivity.applovinInterstitialAdManager")
  public static void injectApplovinInterstitialAdManager(SplashActivity instance,
      ApplovinInterstitialAdManager applovinInterstitialAdManager) {
    instance.applovinInterstitialAdManager = applovinInterstitialAdManager;
  }
}
