## Crash 1: 
        
          Fatal Exception: android.view.WindowManager$BadTokenException: Unable to add window -- token android.os.BinderProxy@6204a0e is not valid; is your activity running?
       at android.view.ViewRootImpl.setView(ViewRootImpl.java:1481)
       at android.view.WindowManagerGlobal.addView(WindowManagerGlobal.java:451)
       at android.view.WindowManagerImpl.addView(WindowManagerImpl.java:150)
       at android.app.Dialog.show(Dialog.java:352)
       at com.tqhit.battery.one.utils.OverlayPermissionUtils.showOverlayPermissionDialog(OverlayPermissionUtils.kt:59)
       at com.tqhit.battery.one.activity.animation.AnimationActivity.setupListener$lambda$7$lambda$6(AnimationActivity.java:139)
       at com.tqhit.battery.one.ads.core.ApplovinRewardedAdManager.onAdHidden(ApplovinRewardedAdManager.kt:78)
       at com.applovin.impl.l2.e(SourceFile:28)
       at android.os.Handler.handleCallback(Handler.java:958)
       at android.os.Handler.dispatchMessage(Handler.java:99)
       at android.os.Looper.loopOnce(Looper.java:205)
       at android.os.Looper.loop(Looper.java:294)
       at android.app.ActivityThread.main(ActivityThread.java:8376)
       at java.lang.reflect.Method.invoke(Method.java)
       at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:640)
       at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:982)
        

## Crash 2
          Fatal Exception: java.lang.IllegalStateException: Attempting to launch an unregistered ActivityResultLauncher with contract androidx.activity.result.contract.ActivityResultContracts$StartActivityForResult@cbebdf8 and input Intent { act=android.settings.action.MANAGE_OVERLAY_PERMISSION dat=package: }. You must ensure the ActivityResultLauncher is registered before calling launch().
       at androidx.activity.result.ActivityResultRegistry$register$2.launch(ActivityResultRegistry.kt:123)
       at androidx.activity.result.ActivityResultLauncher.launch(ActivityResultLauncher.java:37)
       at com.tqhit.battery.one.activity.animation.AnimationActivity.setupListener$lambda$7$lambda$6$lambda$4(AnimationActivity.java:143)
       at com.tqhit.battery.one.utils.OverlayPermissionUtils.showOverlayPermissionDialog$lambda$2(OverlayPermissionUtils.java:53)
       at com.tqhit.battery.one.dialog.utils.NotificationDialog.setupListener$lambda$7(NotificationDialog.java:125)
       at android.view.View.performClick(View.java:7511)
       at android.view.View.performClickInternal(View.java:7484)
       at android.view.View.-$$Nest$mperformClickInternal()
       at android.view.View$PerformClick.run(View.java:29472)
       at android.os.Handler.handleCallback(Handler.java:942)
       at android.os.Handler.dispatchMessage(Handler.java:99)
       at android.os.Looper.loopOnce(Looper.java:201)
       at android.os.Looper.loop(Looper.java:288)
       at android.app.ActivityThread.main(ActivityThread.java:8046)
       at java.lang.reflect.Method.invoke(Method.java)
       at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:703)
       at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:911)
        

## Crash 3
          Fatal Exception: java.lang.IllegalArgumentException: You cannot start a load for a destroyed activity
       at com.bumptech.glide.manager.RequestManagerRetriever.assertNotDestroyed(RequestManagerRetriever.java:236)
       at com.bumptech.glide.manager.RequestManagerRetriever.get(RequestManagerRetriever.java:110)
       at com.bumptech.glide.manager.RequestManagerRetriever.get(RequestManagerRetriever.java:176)
       at com.bumptech.glide.Glide.with(Glide.java:634)
       at com.tqhit.battery.one.fragment.main.animation.adapter.AnimationViewHolder.loadThumbnailWithGlide(AnimationAdapter.kt:236)
       at com.tqhit.battery.one.fragment.main.animation.adapter.AnimationViewHolder.access$loadThumbnailWithGlide(AnimationViewHolder.java:74)
       at com.tqhit.battery.one.fragment.main.animation.adapter.AnimationViewHolder$loadThumbnailWithPreloading$1.invokeSuspend(AnimationAdapter.kt:219)
       at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
       at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
       at android.os.Handler.handleCallback(Handler.java:958)
       at android.os.Handler.dispatchMessage(Handler.java:99)
       at android.os.Looper.loopOnce(Looper.java:222)
       at android.os.Looper.loop(Looper.java:314)
       at android.app.ActivityThread.main(ActivityThread.java:8779)
       at java.lang.reflect.Method.invoke(Method.java)
       at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:569)
       at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1090)
    

## Crash 4
          Fatal Exception: java.lang.RuntimeException: Unable to start activity ComponentInfo{com.fc.p.tj.charginganimation.batterycharging.chargeeffect/com.tqhit.battery.one.activity.splash.SplashActivity}: java.lang.RuntimeException: Window couldn't find content container view
       at android.app.ActivityThread.performLaunchActivity(ActivityThread.java:3050)
       at android.app.ActivityThread.handleLaunchActivity(ActivityThread.java:3197)
       at android.app.servertransaction.LaunchActivityItem.execute(LaunchActivityItem.java:78)
       at android.app.servertransaction.TransactionExecutor.executeCallbacks(TransactionExecutor.java:108)
       at android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:68)
       at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1892)
       at android.os.Handler.dispatchMessage(Handler.java:106)
       at android.os.Looper.loop(Looper.java:192)
       at android.app.ActivityThread.main(ActivityThread.java:7163)
       at java.lang.reflect.Method.invoke(Method.java)
       at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:504)
       at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:858)
        