@echo off
echo ========================================
echo TJ_BatteryOne Splash Timeout Test Script
echo ========================================
echo.

echo Testing splash screen timeout mechanisms...
echo Application ID: com.fc.p.tj.charginganimation.batterycharging.chargeeffect
echo.

echo [1/6] Building debug APK...
call gradlew assembleDebug
if %ERRORLEVEL% neq 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo.
echo [2/6] Installing APK...
adb install -r app\build\outputs\apk\debug\app-debug.apk
if %ERRORLEVEL% neq 0 (
    echo ERROR: Installation failed
    pause
    exit /b 1
)

echo.
echo [3/6] Starting logcat monitoring for timeout mechanisms...
echo Monitoring tags: SPLASH_PROGRESS, STARTUP_TIMING, TIMEOUT_VIOLATION, TIMEOUT_WARNING
echo.

start "Logcat Monitor" cmd /k "adb logcat -s SplashActivity:* InitializationProgressManager:* | findstr /i \"SPLASH_PROGRESS STARTUP_TIMING TIMEOUT_VIOLATION TIMEOUT_WARNING\""

echo.
echo [4/6] Clearing logcat buffer...
adb logcat -c

echo.
echo [5/6] Starting cold start test...
echo Measuring cold start performance with timeout monitoring...
adb shell am start -W -n com.fc.p.tj.charginganimation.batterycharging.chargeeffect/com.tqhit.battery.onecom.tqhit.battery.one.activity.splash.SplashActivity

echo.
echo [6/6] Test completed. Check the logcat window for timeout analysis.
echo.
echo Key metrics to verify:
echo - Primary timeout should trigger at 5000ms if initialization not complete
echo - Emergency timeout should trigger at 6000ms if primary fails
echo - Watchdog timeout should trigger at 6500ms if all else fails
echo - No TIMEOUT_VIOLATION messages should appear in normal operation
echo - TIMEOUT_WARNING messages indicate approaching timeout limits
echo.
echo Press any key to run additional stress tests...
pause

echo.
echo ========================================
echo Running Stress Tests
echo ========================================
echo.

echo [STRESS TEST 1] Multiple rapid launches...
for /l %%i in (1,1,5) do (
    echo Launch %%i/5...
    adb shell am force-stop com.fc.p.tj.charginganimation.batterycharging.chargeeffect
    timeout /t 1 /nobreak >nul
    adb shell am start -n com.fc.p.tj.charginganimation.batterycharging.chargeeffect/com.tqhit.battery.one.activity.splash.SplashActivity
    timeout /t 3 /nobreak >nul
)

echo.
echo [STRESS TEST 2] Memory pressure simulation...
echo Launching multiple apps to create memory pressure...
adb shell am start -n com.android.settings/.Settings
adb shell am start -n com.android.chrome/com.google.android.apps.chrome.Main
timeout /t 2 /nobreak >nul

echo Now launching TJ_BatteryOne under memory pressure...
adb shell am force-stop com.fc.p.tj.charginganimation.batterycharging.chargeeffect
adb shell am start -W -n com.fc.p.tj.charginganimation.batterycharging.chargeeffect/com.tqhit.battery.one.activity.splash.SplashActivity

echo.
echo [STRESS TEST 3] Background/foreground cycling...
for /l %%i in (1,1,3) do (
    echo Cycle %%i/3...
    adb shell am start -n com.fc.p.tj.charginganimation.batterycharging.chargeeffect/com.tqhit.battery.one.activity.splash.SplashActivity
    timeout /t 2 /nobreak >nul
    adb shell input keyevent KEYCODE_HOME
    timeout /t 1 /nobreak >nul
    adb shell am start -n com.fc.p.tj.charginganimation.batterycharging.chargeeffect/com.tqhit.battery.one.activity.splash.SplashActivity
    timeout /t 2 /nobreak >nul
)

echo.
echo ========================================
echo Stress Tests Completed
echo ========================================
echo.
echo Check the logcat monitor window for:
echo 1. Any TIMEOUT_VIOLATION messages (should be rare/none)
echo 2. TIMEOUT_WARNING messages (acceptable under stress)
echo 3. Emergency/Watchdog timeout activations
echo 4. Navigation timing consistency
echo.
echo Press any key to exit...
pause
