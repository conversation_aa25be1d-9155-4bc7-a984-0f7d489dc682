package com.tqhit.battery.one.dialog.utils

import android.content.Context
import android.graphics.Color
import android.util.Log
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.graphics.drawable.toDrawable
import com.applovin.mediation.nativeAds.MaxNativeAdView
import com.applovin.mediation.nativeAds.MaxNativeAdViewBinder
import com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog
import com.tqhit.battery.one.R
import com.tqhit.battery.one.ads.core.ApplovinNativeAdManager
import com.tqhit.battery.one.databinding.DialogNotificationBinding
import dagger.hilt.android.EntryPointAccessors
import dagger.hilt.android.qualifiers.ActivityContext

class NotificationDialog(
    @ActivityContext private val context: Context,
    private val title: String,
    private val message: String,
    private val onConfirm: () -> Unit = {},
    private val onCancel: () -> Unit = {}
) : AdLibBaseDialog<DialogNotificationBinding>(context) {

    companion object {
        private const val TAG = "NotificationDialog"  
    }

    override val binding by lazy { DialogNotificationBinding.inflate(layoutInflater) }

    private val maxNativeAdView: MaxNativeAdView by lazy {
        createNativeAdView()
    }

    private val applovinNativeAdManager: ApplovinNativeAdManager by lazy {
        EntryPointAccessors.fromApplication(
            context.applicationContext,
            ApplovinAdEntryPoint::class.java
        ).applovinNativeAdManager()
    }

    override fun initWindow() {
        window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        val layoutParams = WindowManager.LayoutParams()
        layoutParams.copyFrom(window?.attributes)
        window?.attributes = layoutParams
        setupNativeAd()

        setCanceledOnTouchOutside(false)
    }

    override fun setupUI() {
        try {
            Log.d(TAG, "Setting up notification dialog UI - Title: '$title', Message: '$message'")
            
            // Call super.setupUI() with error handling for ad-related failures
            try {
                super.setupUI()
            } catch (e: Exception) {
                Log.w(TAG, "Error in super.setupUI() - likely ad initialization failure, continuing with basic setup", e)
                // Continue without calling super.setupUI() if it fails due to ad issues
            }

            binding.upText.text = title
            binding.mainText.text = message
            
            Log.d(TAG, "Notification dialog UI setup completed successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error setting up notification dialog UI", e)
            // Fallback: Set basic text content even if other UI setup fails
            try {
                binding.upText.text = title
                binding.mainText.text = message
            } catch (fallbackException: Exception) {
                Log.e(TAG, "Critical error: Failed to set dialog text content", fallbackException)
            }
        }
    }

    private fun setupNativeAd(){

        val container = binding.nativeAd

        applovinNativeAdManager.loadNativeAd(
            onAdLoaded = {
                if (ownerActivity?.isDestroyed == true || ownerActivity?.isFinishing == true || !isShowing) {
                    return@loadNativeAd
                }
                applovinNativeAdManager.removeCachedNativeAd(it)
                applovinNativeAdManager.render(it, maxNativeAdView)
                container.removeAllViews()
                container.hideShimmer()
                container.addView(maxNativeAdView)
            },
            onAdLoadFailed = { errorMsg ->
                Log.e("NativeAd", "Failed to load: $errorMsg")
            }
        )

    }

    private fun createNativeAdView(): MaxNativeAdView
    {
        val binder: MaxNativeAdViewBinder =
            MaxNativeAdViewBinder.Builder(R.layout.layout_native_ads)
                .setTitleTextViewId(R.id.title_text_view)
                .setBodyTextViewId(R.id.body_text_view)
                .setStarRatingContentViewGroupId(R.id.star_rating_view )
                .setAdvertiserTextViewId(R.id.advertiser_text_view)
                .setIconImageViewId(R.id.icon_image_view)
                .setMediaContentViewGroupId(R.id.media_view_container)
                .setOptionsContentViewGroupId(R.id.ad_options_view)
                .setCallToActionButtonId(R.id.cta_button)
                .build()
        return MaxNativeAdView(binder, context)
    }

    override fun setupListener() {
        super.setupListener()

        binding.confirmChangeCapacityError.setOnClickListener {
            dismiss()
            onConfirm()
        }

        binding.exit.setOnClickListener {
            onCancel()
            dismiss()
        }
    }
}