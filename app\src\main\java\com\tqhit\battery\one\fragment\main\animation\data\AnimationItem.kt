package com.tqhit.battery.one.fragment.main.animation.data

import com.google.gson.annotations.SerializedName

data class AnimationItem(
    @SerializedName("isPremium") val isPremium: Boolean,
    @SerializedName("mediaOriginal") val mediaOriginal: String,
    @SerializedName("thumbnail") val thumbnail: String
)

/**
 * Data class representing a thumbnail item for preloading operations.
 * Contains essential information needed for thumbnail downloading and caching.
 */
data class ThumbnailItem(
    val thumbnailUrl: String,
    val categoryName: String,
    val animationMediaUrl: String,
    val isPremium: Boolean = false
)

/**
 * Represents the preload status of an animation item
 */
enum class PreloadStatus {
    NOT_STARTED,    // Preloading has not been initiated
    IN_PROGRESS,    // Currently downloading
    COMPLETED,      // Successfully downloaded and available locally
    FAILED,         // Download failed
    EXPIRED         // Local file exists but may be outdated
}

/**
 * Represents the preload status of a thumbnail item
 */
enum class ThumbnailPreloadStatus {
    NOT_STARTED,    // Thumbnail preloading has not been initiated
    IN_PROGRESS,    // Currently downloading thumbnail
    COMPLETED,      // Successfully downloaded and available locally
    FAILED,         // Thumbnail download failed
    EXPIRED         // Local thumbnail file exists but may be outdated
}

/**
 * Data class representing a preloaded animation with metadata
 */
data class PreloadedAnimationItem(
    val mediaOriginal: String,
    val localFilePath: String,
    val status: PreloadStatus,
    val downloadTimestamp: Long,
    val fileSizeBytes: Long,
    val errorMessage: String? = null
)

/**
 * Data class representing a preloaded thumbnail with metadata
 */
data class PreloadedThumbnailItem(
    val thumbnailUrl: String,
    val localFilePath: String,
    val status: ThumbnailPreloadStatus,
    val downloadTimestamp: Long,
    val fileSizeBytes: Long,
    val categoryName: String,
    val animationMediaUrl: String,
    val errorMessage: String? = null
)

/**
 * Sealed class representing the result of a preload operation
 */
sealed class PreloadResult {
    data class Success(
        val preloadedItem: PreloadedAnimationItem
    ) : PreloadResult()

    data class Failure(
        val mediaOriginal: String,
        val errorMessage: String,
        val exception: Throwable? = null
    ) : PreloadResult()

    data class AlreadyExists(
        val preloadedItem: PreloadedAnimationItem
    ) : PreloadResult()
}

/**
 * Sealed class representing the result of a thumbnail preload operation
 */
sealed class ThumbnailPreloadResult {
    data class Success(
        val preloadedThumbnail: PreloadedThumbnailItem
    ) : ThumbnailPreloadResult()

    data class Failure(
        val thumbnailUrl: String,
        val errorMessage: String,
        val exception: Throwable? = null
    ) : ThumbnailPreloadResult()

    data class AlreadyExists(
        val preloadedThumbnail: PreloadedThumbnailItem
    ) : ThumbnailPreloadResult()
}