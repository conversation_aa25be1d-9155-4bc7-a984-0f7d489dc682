<?xml version="1.0" encoding="utf-8"?>
<!--
 * Reusable back navigation layout component
 * Material 3 styled back button for fragment navigation
 * Positioned in top-left corner with proper touch target size
 -->
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/back_navigation_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="8dp"
    android:paddingBottom="8dp"
    android:paddingStart="8dp"
    android:paddingEnd="8dp">

    <!-- Back navigation button with Material 3 styling -->
    <ImageButton
        android:id="@+id/btn_back_navigation"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:contentDescription="@string/back_navigation"
        android:padding="12dp"
        android:scaleType="centerInside"
        android:src="@drawable/ic_back"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="?attr/colorr" />

    <!-- Optional title text (can be hidden if not needed) -->
    <TextView
        android:id="@+id/tv_back_navigation_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="@string/back_to_others"
        android:textColor="?attr/black"
        android:textSize="18sp"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/btn_back_navigation"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/btn_back_navigation"
        app:layout_constraintTop_toTopOf="@+id/btn_back_navigation" />

</androidx.constraintlayout.widget.ConstraintLayout>
