package com.tqhit.battery.one.dialog.utils

import android.content.Context
import com.tqhit.battery.one.R
import com.tqhit.battery.one.utils.BatteryLogger

/**
 * Dialog that appears after successful animation selection when overlay permission is already granted.
 * Instructs users to unplug and plug back in to test the selected charging animation.
 * 
 * Uses the existing NotificationDialog for UI consistency and follows the established
 * stats module architecture pattern.
 */
class PostAnimationDialog(
    private val context: Context,
    private val onDismiss: () -> Unit = {}
) {

    companion object {
        private const val TAG = "PostAnimationDialog"
        private const val DIALOG_TAG = "PostAnimation_Dialog"

        /**
         * Static method to show the dialog with minimal setup.
         * Convenience method for quick usage without creating an instance.
         *
         * @param context The activity context
         * @param onDismiss Optional callback when dialog is dismissed
         */
        fun showDialog(context: Context, onDismiss: () -> Unit = {}) {
            PostAnimationDialog(context, onDismiss).show()
        }
    }

    /**
     * Shows the post-animation instruction dialog.
     * Displays a message instructing the user to unplug and plug back in to view the animation.
     */
    fun show() {
        BatteryLogger.d(TAG, "Showing post-animation instruction dialog")

        try {
            NotificationDialog(
                context = context,
                title = context.getString(R.string.animation_applied_title),
                message = context.getString(R.string.animation_applied_message),
                onConfirm = {
                    BatteryLogger.d(DIALOG_TAG, "User acknowledged post-animation instructions")
                    onDismiss()
                },
                onCancel = {
                    BatteryLogger.d(DIALOG_TAG, "User dismissed post-animation dialog")
                    onDismiss()
                }
            ).show()
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Failed to show post-animation dialog", e)
            // Fallback: call onDismiss to ensure flow continues
            onDismiss()
        }
    }
}
