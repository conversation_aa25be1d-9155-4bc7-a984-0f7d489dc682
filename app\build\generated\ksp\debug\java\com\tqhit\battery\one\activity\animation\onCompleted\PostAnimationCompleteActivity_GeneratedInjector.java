package com.tqhit.battery.one.activity.animation.onCompleted;

import dagger.hilt.InstallIn;
import dagger.hilt.android.components.ActivityComponent;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.internal.GeneratedEntryPoint;
import javax.annotation.processing.Generated;

@OriginatingElement(
    topLevelClass = PostAnimationCompleteActivity.class
)
@GeneratedEntryPoint
@InstallIn(ActivityComponent.class)
@Generated("dagger.hilt.android.processor.internal.androidentrypoint.InjectorEntryPointGenerator")
public interface PostAnimationCompleteActivity_GeneratedInjector {
  void injectPostAnimationCompleteActivity(
      PostAnimationCompleteActivity postAnimationCompleteActivity);
}
