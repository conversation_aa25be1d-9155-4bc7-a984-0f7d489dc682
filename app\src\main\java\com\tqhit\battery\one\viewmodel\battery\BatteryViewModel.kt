package com.tqhit.battery.one.viewmodel.battery

import android.content.Context
import android.content.Intent
import androidx.lifecycle.ViewModel
import com.tqhit.battery.one.repository.BatteryRepository
// import com.tqhit.battery.one.service.BatteryMonitorService // DEPRECATED: Replaced by CoreBatteryStatsService
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import jakarta.inject.Inject

/**
 * ViewModel that provides battery-related data from the BatteryRepository.
 * 
 * @deprecated Charge-related functionality is being migrated to com.tqhit.battery.one.features.charge.presentation.NewChargeViewModel
 * that follows clean architecture principles. Discharge functionality is already migrated to NewDischargeViewModel.
 */
@HiltViewModel
class BatteryViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    private val repository: BatteryRepository
) : ViewModel() {

    // Expose repository's StateFlows
    val batteryPercentage = repository.batteryPercentage
    val batteryHealth = repository.batteryHealth
    val totalChargeSession = repository.totalChargeSession
    val voltage = repository.voltage
    val power = repository.power
    val amperage = repository.amperage
    val temperature = repository.temperature
    
    /**
     * @deprecated Use NewChargeViewModel for charge rate information
     */
    @Deprecated(
        message = "Use NewChargeViewModel.uiState.chargeEstimates.currentChargingRatePercentPerHour instead",
        replaceWith = ReplaceWith("NewChargeViewModel.uiState.chargeEstimates.currentChargingRatePercentPerHour")
    )
    val chargingRate = repository.chargingRate
    
    val isCharging = repository.isCharging
    
    /**
     * @deprecated Use NewChargeViewModel for time remaining calculations
     */
    @Deprecated(
        message = "Use NewChargeViewModel.uiState.timeToFullText instead",
        replaceWith = ReplaceWith("NewChargeViewModel.uiState.timeToFullText")
    )
    val chargingTimeRemaining = repository.chargingTimeRemaining
    
    /**
     * @deprecated Use NewChargeViewModel for time remaining calculations
     */
    @Deprecated(
        message = "Use NewChargeViewModel.uiState.timeToTargetText instead",
        replaceWith = ReplaceWith("NewChargeViewModel.uiState.timeToTargetText")
    )
    val chargingTimeRemainingToTarget = repository.chargingTimeRemainingToTarget
    
    val screenOnTimeRemaining = repository.screenOnTimeRemaining
    val usageStyleTimeRemaining = repository.usageStyleTimeRemaining
    val screenOffTimeRemaining = repository.screenOffTimeRemaining
    val screenOnTimeRemainingAt100 = repository.screenOnTimeRemainingAt100
    val usageStyleTimeRemainingAt100 = repository.usageStyleTimeRemainingAt100
    val screenOffTimeRemainingAt100 = repository.screenOffTimeRemainingAt100
    
    /**
     * @deprecated Use NewChargeViewModel for charge session data
     */
    @Deprecated(
        message = "Use NewChargeViewModel.uiState.activeChargeSession instead",
        replaceWith = ReplaceWith("NewChargeViewModel.uiState.activeChargeSession")
    )
    val startTimeSession = repository.startTimeSession
    
    /**
     * @deprecated Use NewChargeViewModel for charge session data
     */
    @Deprecated(
        message = "Use NewChargeViewModel.uiState.activeChargeSession instead",
        replaceWith = ReplaceWith("NewChargeViewModel.uiState.activeChargeSession")
    )
    val endTimeSession = repository.endTimeSession
    
    /**
     * @deprecated Use NewChargeViewModel for charge session data
     */
    @Deprecated(
        message = "Use NewChargeViewModel.uiState.activeChargeSession instead",
        replaceWith = ReplaceWith("NewChargeViewModel.uiState.activeChargeSession")
    )
    val averageSpeedSession = repository.averageSpeedSession
    
    /**
     * @deprecated Use NewChargeViewModel for charge session data
     */
    @Deprecated(
        message = "Use NewChargeViewModel.uiState.activeChargeSession instead",
        replaceWith = ReplaceWith("NewChargeViewModel.uiState.activeChargeSession")
    )
    val averageSpeedMilliAmperesSession = repository.averageSpeedMilliAmperesSession
    
    /**
     * @deprecated Use NewChargeViewModel for charge session data
     */
    @Deprecated(
        message = "Use NewChargeViewModel.uiState.activeChargeSession instead",
        replaceWith = ReplaceWith("NewChargeViewModel.uiState.activeChargeSession")
    )
    val startPercentSession = repository.startPercentSession
    
    /**
     * @deprecated Use NewChargeViewModel for charge session data
     */
    @Deprecated(
        message = "Use NewChargeViewModel.uiState.activeChargeSession instead",
        replaceWith = ReplaceWith("NewChargeViewModel.uiState.activeChargeSession")
    )
    val endPercentSession = repository.endPercentSession
    
    /**
     * @deprecated Use NewChargeViewModel for charge session data
     */
    @Deprecated(
        message = "Use NewChargeViewModel.uiState.activeChargeSession instead",
        replaceWith = ReplaceWith("NewChargeViewModel.uiState.activeChargeSession")
    )
    val totalMilliAmperesSession = repository.totalMilliAmperesSession
    
    /**
     * @deprecated Use NewChargeViewModel for charge session data
     */
    @Deprecated(
        message = "Use NewChargeViewModel.uiState.activeChargeSession instead",
        replaceWith = ReplaceWith("NewChargeViewModel.uiState.activeChargeSession")
    )
    val screenOffAverageSpeedSession = repository.screenOffAverageSpeedSession
    
    /**
     * @deprecated Use NewChargeViewModel for charge session data
     */
    @Deprecated(
        message = "Use NewChargeViewModel.uiState.activeChargeSession instead",
        replaceWith = ReplaceWith("NewChargeViewModel.uiState.activeChargeSession")
    )
    val screenOffMilliAmperesSession = repository.screenOffMilliAmperesSession
    
    /**
     * @deprecated Use NewChargeViewModel for charge session data
     */
    @Deprecated(
        message = "Use NewChargeViewModel.uiState.activeChargeSession instead",
        replaceWith = ReplaceWith("NewChargeViewModel.uiState.activeChargeSession")
    )
    val screenOnAverageSpeedSession = repository.screenOnAverageSpeedSession
    
    /**
     * @deprecated Use NewChargeViewModel for charge session data
     */
    @Deprecated(
        message = "Use NewChargeViewModel.uiState.activeChargeSession instead",
        replaceWith = ReplaceWith("NewChargeViewModel.uiState.activeChargeSession")
    )
    val screenOnMilliAmperesSession = repository.screenOnMilliAmperesSession
    
    /**
     * @deprecated Use NewChargeViewModel for charge session data
     */
    @Deprecated(
        message = "Use NewChargeViewModel.uiState.activeChargeSession instead",
        replaceWith = ReplaceWith("NewChargeViewModel.uiState.activeChargeSession")
    )
    val rightNowPercentPerHourSession = repository.rightNowPercentPerHourSession
    
    /**
     * @deprecated Use NewChargeViewModel for overall average statistics
     */
    @Deprecated(
        message = "Use NewChargeViewModel for average charge statistics",
        replaceWith = ReplaceWith("NewChargeViewModel")
    )
    val averageScreenOnSpeed = repository.averageScreenOnSpeed
    
    /**
     * @deprecated Use NewChargeViewModel for overall average statistics
     */
    @Deprecated(
        message = "Use NewChargeViewModel for average charge statistics",
        replaceWith = ReplaceWith("NewChargeViewModel")
    )
    val averageScreenOffSpeed = repository.averageScreenOffSpeed
    
    /**
     * @deprecated Use NewChargeViewModel for overall average statistics
     */
    @Deprecated(
        message = "Use NewChargeViewModel for average charge statistics",
        replaceWith = ReplaceWith("NewChargeViewModel")
    )
    val averageSpeed = repository.averageSpeed
    
    /**
     * @deprecated Use NewChargeViewModel for overall average statistics
     */
    @Deprecated(
        message = "Use NewChargeViewModel for average charge statistics",
        replaceWith = ReplaceWith("NewChargeViewModel")
    )
    val averageScreenOnMilliAmperes = repository.averageScreenOnMilliAmperes
    
    /**
     * @deprecated Use NewChargeViewModel for overall average statistics
     */
    @Deprecated(
        message = "Use NewChargeViewModel for average charge statistics",
        replaceWith = ReplaceWith("NewChargeViewModel")
    )
    val averageScreenOffMilliAmperes = repository.averageScreenOffMilliAmperes
    
    /**
     * @deprecated Use NewChargeViewModel for overall average statistics
     */
    @Deprecated(
        message = "Use NewChargeViewModel for average charge statistics",
        replaceWith = ReplaceWith("NewChargeViewModel")
    )
    val averageMilliAmperes = repository.averageMilliAmperes
    
    val dischargeStartTimeSession = repository.dischargeStartTimeSession
    val dischargeEndTimeSession = repository.dischargeEndTimeSession
    val dischargeAverageSpeedSession = repository.dischargeAverageSpeedSession
    val dischargeAverageSpeedMilliAmperesSession = repository.dischargeAverageSpeedMilliAmperesSession
    val dischargeStartPercentSession = repository.dischargeStartPercentSession
    val dischargeEndPercentSession = repository.dischargeEndPercentSession
    val dischargeTotalMilliAmperesSession = repository.dischargeTotalMilliAmperesSession
    val dischargeScreenOffAverageSpeedSession = repository.dischargeScreenOffAverageSpeedSession
    val dischargeScreenOffMilliAmperesSession = repository.dischargeScreenOffMilliAmperesSession
    val dischargeScreenOnAverageSpeedSession = repository.dischargeScreenOnAverageSpeedSession
    val dischargeScreenOnMilliAmperesSession = repository.dischargeScreenOnMilliAmperesSession
    val dischargeRightNowPercentPerHourSession = repository.dischargeRightNowPercentPerHourSession
    val batteryCapacity = repository.batteryCapacity
    val selectedPercent = repository.selectedPercent

    val dischargeScreenOnTimeCurrentSession: Long
        get() = repository.dischargeScreenOnTimeCurrentSession

    val dischargeScreenOnPercentCurrentSession: Int
        get() = repository.dischargeScreenOnPercentCurrentSession

    val dischargeScreenOffTimeCurrentSession: Long
        get() = repository.dischargeScreenOffTimeCurrentSession

    val dischargeScreenOffPercentCurrentSession: Int
        get() = repository.dischargeScreenOffPercentCurrentSession

    init {
        // Service is started in MainActivity instead, to avoid Background Service Start exceptions
    }

    fun getBatteryCapacity(): Int = repository.getBatteryCapacity()
    fun setBatteryCapacity(capacity: Int) = repository.setBatteryCapacity(capacity)
    fun isIgnoringBatteryOptimizations(): Boolean = repository.isIgnoringBatteryOptimizations()
    fun getBatteryPolarity(): String = repository.getBatteryPolarity()
    fun getSelectedPercent(): Int = repository.getSelectedPercent()
    fun setSelectedPercent(percent: Int) = repository.setSelectedPercent(percent)
    
    /**
     * @deprecated Use the ManageChargeSessionUseCase from the charge feature
     */
    @Deprecated(
        message = "Use ManageChargeSessionUseCase from the charge feature instead",
        replaceWith = ReplaceWith("ManageChargeSessionUseCase")
    )
    fun getCurrentChargingSession() = repository.getCurrentChargingSession()
    
    /**
     * @deprecated Use the ManageChargeSessionUseCase from the charge feature
     */
    @Deprecated(
        message = "Use ManageChargeSessionUseCase from the charge feature instead",
        replaceWith = ReplaceWith("ManageChargeSessionUseCase")
    )
    fun setCurrentChargingSession() = repository.setCurrentChargingSession()
    
    /**
     * @deprecated Use the ManageChargeSessionUseCase from the charge feature
     */
    @Deprecated(
        message = "Use ManageChargeSessionUseCase from the charge feature instead",
        replaceWith = ReplaceWith("ManageChargeSessionUseCase")
    )
    fun getLastValidChargingSession() = repository.getLastValidChargingSession()
    
    /**
     * @deprecated Use the ManageChargeSessionUseCase from the charge feature
     */
    @Deprecated(
        message = "Use ManageChargeSessionUseCase from the charge feature instead",
        replaceWith = ReplaceWith("ManageChargeSessionUseCase")
    )
    fun setLastValidChargingSession() = repository.setLastValidChargingSession()
    
    /**
     * @deprecated Use the ManageChargeSessionUseCase from the charge feature
     */
    @Deprecated(
        message = "Use ManageChargeSessionUseCase from the charge feature instead",
        replaceWith = ReplaceWith("ManageChargeSessionUseCase")
    )
    fun clearChargingSessions() {
        android.util.Log.d("BatteryViewModel", "CLEAR_CHARGE_SESSIONS: === clearChargingSessions CALLED ===")
        android.util.Log.d("BatteryViewModel", "CLEAR_CHARGE_SESSIONS: Stack trace:", Exception("Stack trace"))
        val result = repository.clearChargingSessions()
        android.util.Log.d("BatteryViewModel", "CLEAR_CHARGE_SESSIONS: repository.clearChargingSessions() completed")
        return result
    }
    
    fun getCurrentDischargeSession() = repository.getCurrentDischargeSession()
    fun setCurrentDischargeSession() = repository.setCurrentDischargeSession()
    fun getLastValidDischargeSession() = repository.getLastValidDischargeSession()
    fun setLastValidDischargeSession() = repository.setLastValidDischargeSession()
    fun clearDischargeSessions() {
        android.util.Log.d("BatteryViewModel", "CLEAR_DISCHARGE_SESSIONS: === clearDischargeSessions CALLED ===")
        android.util.Log.d("BatteryViewModel", "CLEAR_DISCHARGE_SESSIONS: Stack trace:", Exception("Stack trace"))
        val result = repository.clearDischargeSessions()
        android.util.Log.d("BatteryViewModel", "CLEAR_DISCHARGE_SESSIONS: repository.clearDischargeSessions() completed")
        return result
    }
    fun getHistoryBatteryForHours(hours: Int) = repository.getHistoryBatteryForHours(hours)
    fun getHistoryTemperatureForHours(hours: Int) = repository.getHistoryTemperatureForHours(hours)
    fun getDailyWearData(days: Int) = repository.getDailyWearData(days)
}