package com.tqhit.battery.one;

import android.app.Activity;
import android.app.Service;
import android.content.SharedPreferences;
import android.view.View;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.SavedStateHandle;
import androidx.lifecycle.ViewModel;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.errorprone.annotations.CanIgnoreReturnValue;
import com.google.firebase.analytics.FirebaseAnalytics;
import com.google.firebase.remoteconfig.FirebaseRemoteConfig;
import com.google.gson.Gson;
import com.tqhit.adlib.sdk.AdLibHiltApplication_MembersInjector;
import com.tqhit.adlib.sdk.adjust.AdjustAnalyticsHelper;
import com.tqhit.adlib.sdk.ads.AdmobConsentHelper;
import com.tqhit.adlib.sdk.ads.AdmobHelper;
import com.tqhit.adlib.sdk.ads.AppOpenHelper;
import com.tqhit.adlib.sdk.ads.BannerHelper;
import com.tqhit.adlib.sdk.ads.InterstitialHelper;
import com.tqhit.adlib.sdk.ads.NativeHelper;
import com.tqhit.adlib.sdk.ads.RewardHelper;
import com.tqhit.adlib.sdk.analytics.AnalyticsTracker;
import com.tqhit.adlib.sdk.data.local.PreferencesHelper;
import com.tqhit.adlib.sdk.di.AdmobModule_ProvideAdmobConsentHelperFactory;
import com.tqhit.adlib.sdk.di.AdmobModule_ProvideAdmobHelperFactory;
import com.tqhit.adlib.sdk.di.AdmobModule_ProvideAppOpenHelperFactory;
import com.tqhit.adlib.sdk.di.AdmobModule_ProvideBannerHelperFactory;
import com.tqhit.adlib.sdk.di.AdmobModule_ProvideInterstitialHelperFactory;
import com.tqhit.adlib.sdk.di.AdmobModule_ProvideNativeHelperFactory;
import com.tqhit.adlib.sdk.di.AdmobModule_ProvideRewardHelperFactory;
import com.tqhit.adlib.sdk.di.AnalyticsModule_ProvideAdjustAnalyticsHelperFactory;
import com.tqhit.adlib.sdk.di.AnalyticsModule_ProvideTrackingManagerFactory;
import com.tqhit.adlib.sdk.di.FirebaseModule_ProvideFirebaseAnalyticsFactory;
import com.tqhit.adlib.sdk.di.FirebaseModule_ProvideFirebaseAnalyticsHelperFactory;
import com.tqhit.adlib.sdk.di.FirebaseModule_ProvideFirebaseRemoteConfigFactory;
import com.tqhit.adlib.sdk.di.FirebaseModule_ProvideFirebaseRemoteConfigHelperFactory;
import com.tqhit.adlib.sdk.di.SharedPreferencesModule_ProvideGsonFactory;
import com.tqhit.adlib.sdk.di.SharedPreferencesModule_ProvidePreferencesHelperFactory;
import com.tqhit.adlib.sdk.di.SharedPreferencesModule_ProvideSharedPreferencesFactory;
import com.tqhit.adlib.sdk.firebase.FirebaseAnalyticsHelper;
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper;
import com.tqhit.adlib.sdk.ui.main.MainActivity;
import com.tqhit.adlib.sdk.ui.main.MainActivity_MembersInjector;
import com.tqhit.battery.one.activity.animation.AnimationActivity;
import com.tqhit.battery.one.activity.animation.AnimationActivity_MembersInjector;
import com.tqhit.battery.one.activity.animation.onCompleted.PostAnimationCompleteActivity;
import com.tqhit.battery.one.activity.animation.onCompleted.PostAnimationCompleteActivity_MembersInjector;
import com.tqhit.battery.one.activity.main.handlers.FragmentLifecycleManager;
import com.tqhit.battery.one.activity.main.handlers.NavigationHandler;
import com.tqhit.battery.one.activity.main.handlers.ServiceManager;
import com.tqhit.battery.one.activity.onboarding.LanguageSelectionActivity;
import com.tqhit.battery.one.activity.onboarding.LanguageSelectionActivity_MembersInjector;
import com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity;
import com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity_MembersInjector;
import com.tqhit.battery.one.activity.password.EnterPasswordActivity;
import com.tqhit.battery.one.activity.password.EnterPasswordActivity_MembersInjector;
import com.tqhit.battery.one.activity.splash.SplashActivity;
import com.tqhit.battery.one.activity.splash.SplashActivity_MembersInjector;
import com.tqhit.battery.one.activity.starting.StartingActivity;
import com.tqhit.battery.one.activity.starting.StartingActivity_MembersInjector;
import com.tqhit.battery.one.ads.core.ApplovinAppOpenAdManager;
import com.tqhit.battery.one.ads.core.ApplovinBannerAdManager;
import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager;
import com.tqhit.battery.one.ads.core.ApplovinNativeAdManager;
import com.tqhit.battery.one.ads.core.ApplovinRewardedAdManager;
import com.tqhit.battery.one.base.LocaleAwareActivity_MembersInjector;
import com.tqhit.battery.one.di.ThumbnailPreloadingModule_ProvideDeferredThumbnailPreloadingServiceFactory;
import com.tqhit.battery.one.di.ThumbnailPreloadingModule_ProvideThumbnailDataServiceFactory;
import com.tqhit.battery.one.di.ThumbnailPreloadingModule_ProvideThumbnailFileManagerFactory;
import com.tqhit.battery.one.di.ThumbnailPreloadingModule_ProvideThumbnailPreloaderFactory;
import com.tqhit.battery.one.di.ThumbnailPreloadingModule_ProvideThumbnailPreloadingRepositoryFactory;
import com.tqhit.battery.one.features.navigation.AppNavigator;
import com.tqhit.battery.one.features.navigation.DynamicNavigationManager;
import com.tqhit.battery.one.features.navigation.SharedNavigationViewModel;
import com.tqhit.battery.one.features.navigation.SharedNavigationViewModel_HiltModules;
import com.tqhit.battery.one.features.navigation.SharedNavigationViewModel_HiltModules_BindsModule_Binds_LazyMapKey;
import com.tqhit.battery.one.features.navigation.SharedNavigationViewModel_HiltModules_KeyModule_Provide_LazyMapKey;
import com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager;
import com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialogFactory;
import com.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository;
import com.tqhit.battery.one.features.stats.charge.cache.PrefsStatsChargeCache;
import com.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase;
import com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment;
import com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment_MembersInjector;
import com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel;
import com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel_HiltModules;
import com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel_HiltModules_BindsModule_Binds_LazyMapKey;
import com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel_HiltModules_KeyModule_Provide_LazyMapKey;
import com.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository;
import com.tqhit.battery.one.features.stats.corebattery.domain.DefaultCoreBatteryStatsProvider;
import com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryServiceHelper;
import com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService;
import com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService_MembersInjector;
import com.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache;
import com.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache;
import com.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver;
import com.tqhit.battery.one.features.stats.discharge.di.StatsDischargeProvidersModule_ProvideScreenStateReceiverFactory;
import com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager;
import com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator;
import com.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator;
import com.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimator;
import com.tqhit.battery.one.features.stats.discharge.domain.GapEstimationCalculator;
import com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator;
import com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService;
import com.tqhit.battery.one.features.stats.discharge.domain.SessionManager;
import com.tqhit.battery.one.features.stats.discharge.domain.SessionMetricsCalculator;
import com.tqhit.battery.one.features.stats.discharge.domain.TimeConverter;
import com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment;
import com.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment_MembersInjector;
import com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel;
import com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel_HiltModules;
import com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel_HiltModules_BindsModule_Binds_LazyMapKey;
import com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel_HiltModules_KeyModule_Provide_LazyMapKey;
import com.tqhit.battery.one.features.stats.discharge.presentation.InfoButtonManager;
import com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository;
import com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService;
import com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper;
import com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService_MembersInjector;
import com.tqhit.battery.one.features.stats.health.cache.DefaultHealthCache;
import com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel;
import com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel_HiltModules;
import com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel_HiltModules_BindsModule_Binds_LazyMapKey;
import com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel_HiltModules_KeyModule_Provide_LazyMapKey;
import com.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository;
import com.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository;
import com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService;
import com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationServiceHelper;
import com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService_MembersInjector;
import com.tqhit.battery.one.fragment.main.HealthFragment;
import com.tqhit.battery.one.fragment.main.HealthFragment_MembersInjector;
import com.tqhit.battery.one.fragment.main.SettingsFragment;
import com.tqhit.battery.one.fragment.main.SettingsFragment_MembersInjector;
import com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment;
import com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment_MembersInjector;
import com.tqhit.battery.one.fragment.main.others.OthersFragment;
import com.tqhit.battery.one.fragment.main.others.OthersFragment_MembersInjector;
import com.tqhit.battery.one.fragment.onboarding.LanguageSelectionViewModel;
import com.tqhit.battery.one.fragment.onboarding.LanguageSelectionViewModel_HiltModules;
import com.tqhit.battery.one.fragment.onboarding.LanguageSelectionViewModel_HiltModules_BindsModule_Binds_LazyMapKey;
import com.tqhit.battery.one.fragment.onboarding.LanguageSelectionViewModel_HiltModules_KeyModule_Provide_LazyMapKey;
import com.tqhit.battery.one.initialization.InitializationProgressManager;
import com.tqhit.battery.one.initialization.ServiceInitializationHelper;
import com.tqhit.battery.one.manager.animation.AnimationFileManager;
import com.tqhit.battery.one.manager.charge.ChargingSessionManager;
import com.tqhit.battery.one.manager.discharge.DischargeSessionManager;
import com.tqhit.battery.one.manager.graph.BatteryHistoryManager;
import com.tqhit.battery.one.manager.graph.TemperatureHistoryManager;
import com.tqhit.battery.one.manager.thumbnail.ThumbnailFileManager;
import com.tqhit.battery.one.repository.AnimationPreloadingRepository;
import com.tqhit.battery.one.repository.AnimationRepository;
import com.tqhit.battery.one.repository.AppRepository;
import com.tqhit.battery.one.repository.BatteryRepository;
import com.tqhit.battery.one.repository.ThumbnailPreloadingRepository;
import com.tqhit.battery.one.service.ChargingOverlayService;
import com.tqhit.battery.one.service.ChargingOverlayServiceHelper;
import com.tqhit.battery.one.service.ChargingOverlayService_MembersInjector;
import com.tqhit.battery.one.service.VibrationService;
import com.tqhit.battery.one.service.animation.AnimationDataService;
import com.tqhit.battery.one.service.animation.AnimationPreloader;
import com.tqhit.battery.one.service.thumbnail.DeferredThumbnailPreloadingService;
import com.tqhit.battery.one.service.thumbnail.ThumbnailDataService;
import com.tqhit.battery.one.service.thumbnail.ThumbnailPreloader;
import com.tqhit.battery.one.utils.PreloadingMonitor;
import com.tqhit.battery.one.utils.VideoUtils;
import com.tqhit.battery.one.viewmodel.AppViewModel;
import com.tqhit.battery.one.viewmodel.AppViewModel_HiltModules;
import com.tqhit.battery.one.viewmodel.AppViewModel_HiltModules_BindsModule_Binds_LazyMapKey;
import com.tqhit.battery.one.viewmodel.AppViewModel_HiltModules_KeyModule_Provide_LazyMapKey;
import com.tqhit.battery.one.viewmodel.animation.AnimationViewModel;
import com.tqhit.battery.one.viewmodel.animation.AnimationViewModel_HiltModules;
import com.tqhit.battery.one.viewmodel.animation.AnimationViewModel_HiltModules_BindsModule_Binds_LazyMapKey;
import com.tqhit.battery.one.viewmodel.animation.AnimationViewModel_HiltModules_KeyModule_Provide_LazyMapKey;
import com.tqhit.battery.one.viewmodel.battery.BatteryViewModel;
import com.tqhit.battery.one.viewmodel.battery.BatteryViewModel_HiltModules;
import com.tqhit.battery.one.viewmodel.battery.BatteryViewModel_HiltModules_BindsModule_Binds_LazyMapKey;
import com.tqhit.battery.one.viewmodel.battery.BatteryViewModel_HiltModules_KeyModule_Provide_LazyMapKey;
import dagger.hilt.android.ActivityRetainedLifecycle;
import dagger.hilt.android.ViewModelLifecycle;
import dagger.hilt.android.internal.builders.ActivityComponentBuilder;
import dagger.hilt.android.internal.builders.ActivityRetainedComponentBuilder;
import dagger.hilt.android.internal.builders.FragmentComponentBuilder;
import dagger.hilt.android.internal.builders.ServiceComponentBuilder;
import dagger.hilt.android.internal.builders.ViewComponentBuilder;
import dagger.hilt.android.internal.builders.ViewModelComponentBuilder;
import dagger.hilt.android.internal.builders.ViewWithFragmentComponentBuilder;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories_InternalFactoryFactory_Factory;
import dagger.hilt.android.internal.managers.ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory;
import dagger.hilt.android.internal.managers.SavedStateHandleHolder;
import dagger.hilt.android.internal.modules.ApplicationContextModule;
import dagger.hilt.android.internal.modules.ApplicationContextModule_ProvideContextFactory;
import dagger.internal.DaggerGenerated;
import dagger.internal.DoubleCheck;
import dagger.internal.LazyClassKeyMap;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import java.util.Map;
import java.util.Set;
import javax.annotation.processing.Generated;

@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class DaggerBatteryApplication_HiltComponents_SingletonC {
  private DaggerBatteryApplication_HiltComponents_SingletonC() {
  }

  public static Builder builder() {
    return new Builder();
  }

  public static final class Builder {
    private ApplicationContextModule applicationContextModule;

    private Builder() {
    }

    public Builder applicationContextModule(ApplicationContextModule applicationContextModule) {
      this.applicationContextModule = Preconditions.checkNotNull(applicationContextModule);
      return this;
    }

    public BatteryApplication_HiltComponents.SingletonC build() {
      Preconditions.checkBuilderRequirement(applicationContextModule, ApplicationContextModule.class);
      return new SingletonCImpl(applicationContextModule);
    }
  }

  private static final class ActivityRetainedCBuilder implements BatteryApplication_HiltComponents.ActivityRetainedC.Builder {
    private final SingletonCImpl singletonCImpl;

    private SavedStateHandleHolder savedStateHandleHolder;

    private ActivityRetainedCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ActivityRetainedCBuilder savedStateHandleHolder(
        SavedStateHandleHolder savedStateHandleHolder) {
      this.savedStateHandleHolder = Preconditions.checkNotNull(savedStateHandleHolder);
      return this;
    }

    @Override
    public BatteryApplication_HiltComponents.ActivityRetainedC build() {
      Preconditions.checkBuilderRequirement(savedStateHandleHolder, SavedStateHandleHolder.class);
      return new ActivityRetainedCImpl(singletonCImpl, savedStateHandleHolder);
    }
  }

  private static final class ActivityCBuilder implements BatteryApplication_HiltComponents.ActivityC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private Activity activity;

    private ActivityCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ActivityCBuilder activity(Activity activity) {
      this.activity = Preconditions.checkNotNull(activity);
      return this;
    }

    @Override
    public BatteryApplication_HiltComponents.ActivityC build() {
      Preconditions.checkBuilderRequirement(activity, Activity.class);
      return new ActivityCImpl(singletonCImpl, activityRetainedCImpl, activity);
    }
  }

  private static final class FragmentCBuilder implements BatteryApplication_HiltComponents.FragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private Fragment fragment;

    private FragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public FragmentCBuilder fragment(Fragment fragment) {
      this.fragment = Preconditions.checkNotNull(fragment);
      return this;
    }

    @Override
    public BatteryApplication_HiltComponents.FragmentC build() {
      Preconditions.checkBuilderRequirement(fragment, Fragment.class);
      return new FragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragment);
    }
  }

  private static final class ViewWithFragmentCBuilder implements BatteryApplication_HiltComponents.ViewWithFragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private View view;

    private ViewWithFragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;
    }

    @Override
    public ViewWithFragmentCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public BatteryApplication_HiltComponents.ViewWithFragmentC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewWithFragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl, view);
    }
  }

  private static final class ViewCBuilder implements BatteryApplication_HiltComponents.ViewC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private View view;

    private ViewCBuilder(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public ViewCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public BatteryApplication_HiltComponents.ViewC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, view);
    }
  }

  private static final class ViewModelCBuilder implements BatteryApplication_HiltComponents.ViewModelC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private SavedStateHandle savedStateHandle;

    private ViewModelLifecycle viewModelLifecycle;

    private ViewModelCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ViewModelCBuilder savedStateHandle(SavedStateHandle handle) {
      this.savedStateHandle = Preconditions.checkNotNull(handle);
      return this;
    }

    @Override
    public ViewModelCBuilder viewModelLifecycle(ViewModelLifecycle viewModelLifecycle) {
      this.viewModelLifecycle = Preconditions.checkNotNull(viewModelLifecycle);
      return this;
    }

    @Override
    public BatteryApplication_HiltComponents.ViewModelC build() {
      Preconditions.checkBuilderRequirement(savedStateHandle, SavedStateHandle.class);
      Preconditions.checkBuilderRequirement(viewModelLifecycle, ViewModelLifecycle.class);
      return new ViewModelCImpl(singletonCImpl, activityRetainedCImpl, savedStateHandle, viewModelLifecycle);
    }
  }

  private static final class ServiceCBuilder implements BatteryApplication_HiltComponents.ServiceC.Builder {
    private final SingletonCImpl singletonCImpl;

    private Service service;

    private ServiceCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ServiceCBuilder service(Service service) {
      this.service = Preconditions.checkNotNull(service);
      return this;
    }

    @Override
    public BatteryApplication_HiltComponents.ServiceC build() {
      Preconditions.checkBuilderRequirement(service, Service.class);
      return new ServiceCImpl(singletonCImpl, service);
    }
  }

  private static final class ViewWithFragmentCImpl extends BatteryApplication_HiltComponents.ViewWithFragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private final ViewWithFragmentCImpl viewWithFragmentCImpl = this;

    ViewWithFragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;


    }
  }

  private static final class FragmentCImpl extends BatteryApplication_HiltComponents.FragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl = this;

    FragmentCImpl(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl, Fragment fragmentParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }

    InfoButtonManager infoButtonManager() {
      return new InfoButtonManager(activityCImpl.activity, singletonCImpl.appPowerConsumptionDialogFactoryProvider.get(), singletonCImpl.usageStatsPermissionManagerProvider.get());
    }

    @Override
    public void injectStatsChargeFragment(StatsChargeFragment arg0) {
      injectStatsChargeFragment2(arg0);
    }

    @Override
    public void injectDischargeFragment(DischargeFragment arg0) {
      injectDischargeFragment2(arg0);
    }

    @Override
    public void injectHealthFragment(HealthFragment arg0) {
      injectHealthFragment2(arg0);
    }

    @Override
    public void injectSettingsFragment(SettingsFragment arg0) {
      injectSettingsFragment2(arg0);
    }

    @Override
    public void injectAnimationGridFragment(AnimationGridFragment arg0) {
      injectAnimationGridFragment2(arg0);
    }

    @Override
    public void injectOthersFragment(OthersFragment arg0) {
      injectOthersFragment2(arg0);
    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return activityCImpl.getHiltInternalFactoryFactory();
    }

    @Override
    public ViewWithFragmentComponentBuilder viewWithFragmentComponentBuilder() {
      return new ViewWithFragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl);
    }

    @CanIgnoreReturnValue
    private StatsChargeFragment injectStatsChargeFragment2(StatsChargeFragment instance) {
      StatsChargeFragment_MembersInjector.injectVibrationService(instance, singletonCImpl.vibrationServiceProvider.get());
      StatsChargeFragment_MembersInjector.injectAppNavigator(instance, singletonCImpl.appNavigatorProvider.get());
      StatsChargeFragment_MembersInjector.injectApplovinNativeAdManager(instance, singletonCImpl.applovinNativeAdManagerProvider.get());
      StatsChargeFragment_MembersInjector.injectApplovinInterstitialAdManager(instance, singletonCImpl.applovinInterstitialAdManagerProvider.get());
      StatsChargeFragment_MembersInjector.injectAppLifecycleManager(instance, singletonCImpl.appLifecycleManagerProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private DischargeFragment injectDischargeFragment2(DischargeFragment instance2) {
      DischargeFragment_MembersInjector.injectInfoButtonManager(instance2, infoButtonManager());
      DischargeFragment_MembersInjector.injectApplovinNativeAdManager(instance2, singletonCImpl.applovinNativeAdManagerProvider.get());
      DischargeFragment_MembersInjector.injectApplovinInterstitialAdManager(instance2, singletonCImpl.applovinInterstitialAdManagerProvider.get());
      DischargeFragment_MembersInjector.injectTimeConverter(instance2, singletonCImpl.timeConverterProvider.get());
      DischargeFragment_MembersInjector.injectAppLifecycleManager(instance2, singletonCImpl.appLifecycleManagerProvider.get());
      return instance2;
    }

    @CanIgnoreReturnValue
    private HealthFragment injectHealthFragment2(HealthFragment instance3) {
      HealthFragment_MembersInjector.injectApplovinInterstitialAdManager(instance3, singletonCImpl.applovinInterstitialAdManagerProvider.get());
      HealthFragment_MembersInjector.injectChargingSessionManager(instance3, singletonCImpl.chargingSessionManagerProvider.get());
      HealthFragment_MembersInjector.injectHistoryBatteryRepository(instance3, singletonCImpl.historyBatteryRepositoryProvider.get());
      HealthFragment_MembersInjector.injectCoreBatteryStatsProvider(instance3, singletonCImpl.defaultCoreBatteryStatsProvider.get());
      HealthFragment_MembersInjector.injectAppNavigator(instance3, singletonCImpl.appNavigatorProvider.get());
      HealthFragment_MembersInjector.injectApplovinNativeAdManager(instance3, singletonCImpl.applovinNativeAdManagerProvider.get());
      return instance3;
    }

    @CanIgnoreReturnValue
    private SettingsFragment injectSettingsFragment2(SettingsFragment instance4) {
      SettingsFragment_MembersInjector.injectApplovinInterstitialAdManager(instance4, singletonCImpl.applovinInterstitialAdManagerProvider.get());
      SettingsFragment_MembersInjector.injectPreferencesHelper(instance4, singletonCImpl.providePreferencesHelperProvider.get());
      SettingsFragment_MembersInjector.injectApplovinNativeAdManager(instance4, singletonCImpl.applovinNativeAdManagerProvider.get());
      SettingsFragment_MembersInjector.injectAppNavigator(instance4, singletonCImpl.appNavigatorProvider.get());
      return instance4;
    }

    @CanIgnoreReturnValue
    private AnimationGridFragment injectAnimationGridFragment2(AnimationGridFragment instance5) {
      AnimationGridFragment_MembersInjector.injectApplovinInterstitialAdManager(instance5, singletonCImpl.applovinInterstitialAdManagerProvider.get());
      AnimationGridFragment_MembersInjector.injectRemoteConfigHelper(instance5, singletonCImpl.provideFirebaseRemoteConfigHelperProvider.get());
      AnimationGridFragment_MembersInjector.injectAppRepository(instance5, singletonCImpl.appRepositoryProvider.get());
      AnimationGridFragment_MembersInjector.injectVideoUtils(instance5, singletonCImpl.videoUtilsProvider.get());
      AnimationGridFragment_MembersInjector.injectPreloadingMonitor(instance5, singletonCImpl.preloadingMonitorProvider.get());
      AnimationGridFragment_MembersInjector.injectThumbnailPreloadingRepository(instance5, singletonCImpl.provideThumbnailPreloadingRepositoryProvider.get());
      return instance5;
    }

    @CanIgnoreReturnValue
    private OthersFragment injectOthersFragment2(OthersFragment instance6) {
      OthersFragment_MembersInjector.injectCoreBatteryStatsProvider(instance6, singletonCImpl.defaultCoreBatteryStatsProvider.get());
      OthersFragment_MembersInjector.injectDynamicNavigationManager(instance6, singletonCImpl.dynamicNavigationManagerProvider.get());
      OthersFragment_MembersInjector.injectAppNavigator(instance6, singletonCImpl.appNavigatorProvider.get());
      OthersFragment_MembersInjector.injectApplovinInterstitialAdManager(instance6, singletonCImpl.applovinInterstitialAdManagerProvider.get());
      OthersFragment_MembersInjector.injectApplovinNativeAdManager(instance6, singletonCImpl.applovinNativeAdManagerProvider.get());
      return instance6;
    }
  }

  private static final class ViewCImpl extends BatteryApplication_HiltComponents.ViewC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final ViewCImpl viewCImpl = this;

    ViewCImpl(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }
  }

  private static final class ActivityCImpl extends BatteryApplication_HiltComponents.ActivityC {
    private final Activity activity;

    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl = this;

    ActivityCImpl(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        Activity activityParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activity = activityParam;

    }

    @Override
    public void injectMainActivity(MainActivity arg0) {
      injectMainActivity2(arg0);
    }

    @Override
    public void injectMainActivity(com.tqhit.battery.one.activity.main.MainActivity arg0) {
      injectMainActivity3(arg0);
    }

    @Override
    public void injectAnimationActivity(AnimationActivity arg0) {
      injectAnimationActivity2(arg0);
    }

    @Override
    public void injectPostAnimationCompleteActivity(PostAnimationCompleteActivity arg0) {
      injectPostAnimationCompleteActivity2(arg0);
    }

    @Override
    public void injectLanguageSelectionActivity(LanguageSelectionActivity arg0) {
      injectLanguageSelectionActivity2(arg0);
    }

    @Override
    public void injectChargingOverlayActivity(ChargingOverlayActivity arg0) {
      injectChargingOverlayActivity2(arg0);
    }

    @Override
    public void injectEnterPasswordActivity(EnterPasswordActivity arg0) {
      injectEnterPasswordActivity2(arg0);
    }

    @Override
    public void injectSplashActivity(SplashActivity arg0) {
      injectSplashActivity2(arg0);
    }

    @Override
    public void injectStartingActivity(StartingActivity arg0) {
      injectStartingActivity2(arg0);
    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return DefaultViewModelFactories_InternalFactoryFactory_Factory.newInstance(getViewModelKeys(), new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl));
    }

    @Override
    public Map<Class<?>, Boolean> getViewModelKeys() {
      return LazyClassKeyMap.<Boolean>of(ImmutableMap.<String, Boolean>builderWithExpectedSize(8).put(AnimationViewModel_HiltModules_KeyModule_Provide_LazyMapKey.lazyClassKeyName, AnimationViewModel_HiltModules.KeyModule.provide()).put(AppViewModel_HiltModules_KeyModule_Provide_LazyMapKey.lazyClassKeyName, AppViewModel_HiltModules.KeyModule.provide()).put(BatteryViewModel_HiltModules_KeyModule_Provide_LazyMapKey.lazyClassKeyName, BatteryViewModel_HiltModules.KeyModule.provide()).put(DischargeViewModel_HiltModules_KeyModule_Provide_LazyMapKey.lazyClassKeyName, DischargeViewModel_HiltModules.KeyModule.provide()).put(HealthViewModel_HiltModules_KeyModule_Provide_LazyMapKey.lazyClassKeyName, HealthViewModel_HiltModules.KeyModule.provide()).put(LanguageSelectionViewModel_HiltModules_KeyModule_Provide_LazyMapKey.lazyClassKeyName, LanguageSelectionViewModel_HiltModules.KeyModule.provide()).put(SharedNavigationViewModel_HiltModules_KeyModule_Provide_LazyMapKey.lazyClassKeyName, SharedNavigationViewModel_HiltModules.KeyModule.provide()).put(StatsChargeViewModel_HiltModules_KeyModule_Provide_LazyMapKey.lazyClassKeyName, StatsChargeViewModel_HiltModules.KeyModule.provide()).build());
    }

    @Override
    public ViewModelComponentBuilder getViewModelComponentBuilder() {
      return new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public FragmentComponentBuilder fragmentComponentBuilder() {
      return new FragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    @Override
    public ViewComponentBuilder viewComponentBuilder() {
      return new ViewCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    @CanIgnoreReturnValue
    private MainActivity injectMainActivity2(MainActivity instance) {
      MainActivity_MembersInjector.injectAdmobConsentHelper(instance, singletonCImpl.provideAdmobConsentHelperProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private com.tqhit.battery.one.activity.main.MainActivity injectMainActivity3(
        com.tqhit.battery.one.activity.main.MainActivity instance2) {
      LocaleAwareActivity_MembersInjector.injectAppRepository(instance2, singletonCImpl.appRepositoryProvider.get());
      com.tqhit.battery.one.activity.main.MainActivity_MembersInjector.injectStatsChargeRepository(instance2, singletonCImpl.defaultStatsChargeRepositoryProvider.get());
      com.tqhit.battery.one.activity.main.MainActivity_MembersInjector.injectAppRepository(instance2, singletonCImpl.appRepositoryProvider.get());
      com.tqhit.battery.one.activity.main.MainActivity_MembersInjector.injectApplovinBannerAdManager(instance2, singletonCImpl.applovinBannerAdManagerProvider.get());
      com.tqhit.battery.one.activity.main.MainActivity_MembersInjector.injectRemoteConfigHelper(instance2, singletonCImpl.provideFirebaseRemoteConfigHelperProvider.get());
      com.tqhit.battery.one.activity.main.MainActivity_MembersInjector.injectEnhancedDischargeTimerServiceHelper(instance2, singletonCImpl.enhancedDischargeTimerServiceHelperProvider.get());
      com.tqhit.battery.one.activity.main.MainActivity_MembersInjector.injectDischargeSessionRepository(instance2, singletonCImpl.dischargeSessionRepositoryProvider.get());
      com.tqhit.battery.one.activity.main.MainActivity_MembersInjector.injectUnifiedBatteryNotificationServiceHelper(instance2, singletonCImpl.unifiedBatteryNotificationServiceHelperProvider.get());
      com.tqhit.battery.one.activity.main.MainActivity_MembersInjector.injectUsageStatsPermissionManager(instance2, singletonCImpl.usageStatsPermissionManagerProvider.get());
      com.tqhit.battery.one.activity.main.MainActivity_MembersInjector.injectDynamicNavigationManager(instance2, singletonCImpl.dynamicNavigationManagerProvider.get());
      com.tqhit.battery.one.activity.main.MainActivity_MembersInjector.injectAppNavigator(instance2, singletonCImpl.appNavigatorProvider.get());
      com.tqhit.battery.one.activity.main.MainActivity_MembersInjector.injectCoreBatteryStatsProvider(instance2, singletonCImpl.defaultCoreBatteryStatsProvider.get());
      com.tqhit.battery.one.activity.main.MainActivity_MembersInjector.injectNavigationHandler(instance2, singletonCImpl.navigationHandlerProvider.get());
      com.tqhit.battery.one.activity.main.MainActivity_MembersInjector.injectServiceManager(instance2, singletonCImpl.serviceManagerProvider.get());
      com.tqhit.battery.one.activity.main.MainActivity_MembersInjector.injectFragmentLifecycleManager(instance2, singletonCImpl.fragmentLifecycleManagerProvider.get());
      com.tqhit.battery.one.activity.main.MainActivity_MembersInjector.injectApplovinInterstitialAdManager(instance2, singletonCImpl.applovinInterstitialAdManagerProvider.get());
      return instance2;
    }

    @CanIgnoreReturnValue
    private AnimationActivity injectAnimationActivity2(AnimationActivity instance3) {
      AnimationActivity_MembersInjector.injectRemoteConfigHelper(instance3, singletonCImpl.provideFirebaseRemoteConfigHelperProvider.get());
      AnimationActivity_MembersInjector.injectApplovinRewardedAdManager(instance3, singletonCImpl.applovinRewardedAdManagerProvider.get());
      AnimationActivity_MembersInjector.injectApplovinInterstitialAdManager(instance3, singletonCImpl.applovinInterstitialAdManagerProvider.get());
      AnimationActivity_MembersInjector.injectAppRepository(instance3, singletonCImpl.appRepositoryProvider.get());
      AnimationActivity_MembersInjector.injectVideoUtils(instance3, singletonCImpl.videoUtilsProvider.get());
      return instance3;
    }

    @CanIgnoreReturnValue
    private PostAnimationCompleteActivity injectPostAnimationCompleteActivity2(
        PostAnimationCompleteActivity instance4) {
      PostAnimationCompleteActivity_MembersInjector.injectCoreBatteryStatsProvider(instance4, singletonCImpl.defaultCoreBatteryStatsProvider.get());
      PostAnimationCompleteActivity_MembersInjector.injectAppNavigator(instance4, singletonCImpl.appNavigatorProvider.get());
      return instance4;
    }

    @CanIgnoreReturnValue
    private LanguageSelectionActivity injectLanguageSelectionActivity2(
        LanguageSelectionActivity instance5) {
      LocaleAwareActivity_MembersInjector.injectAppRepository(instance5, singletonCImpl.appRepositoryProvider.get());
      LanguageSelectionActivity_MembersInjector.injectApplovinNativeAdManager(instance5, singletonCImpl.applovinNativeAdManagerProvider.get());
      return instance5;
    }

    @CanIgnoreReturnValue
    private ChargingOverlayActivity injectChargingOverlayActivity2(
        ChargingOverlayActivity instance6) {
      ChargingOverlayActivity_MembersInjector.injectAppRepository(instance6, singletonCImpl.appRepositoryProvider.get());
      return instance6;
    }

    @CanIgnoreReturnValue
    private EnterPasswordActivity injectEnterPasswordActivity2(EnterPasswordActivity instance7) {
      EnterPasswordActivity_MembersInjector.injectAppRepository(instance7, singletonCImpl.appRepositoryProvider.get());
      return instance7;
    }

    @CanIgnoreReturnValue
    private SplashActivity injectSplashActivity2(SplashActivity instance8) {
      SplashActivity_MembersInjector.injectInitializationProgressManager(instance8, singletonCImpl.initializationProgressManagerProvider.get());
      SplashActivity_MembersInjector.injectApplovinInterstitialAdManager(instance8, singletonCImpl.applovinInterstitialAdManagerProvider.get());
      return instance8;
    }

    @CanIgnoreReturnValue
    private StartingActivity injectStartingActivity2(StartingActivity instance9) {
      LocaleAwareActivity_MembersInjector.injectAppRepository(instance9, singletonCImpl.appRepositoryProvider.get());
      StartingActivity_MembersInjector.injectApplovinInterstitialAdManager(instance9, singletonCImpl.applovinInterstitialAdManagerProvider.get());
      StartingActivity_MembersInjector.injectApplovinNativeAdManager(instance9, singletonCImpl.applovinNativeAdManagerProvider.get());
      StartingActivity_MembersInjector.injectRemoteConfigHelper(instance9, singletonCImpl.provideFirebaseRemoteConfigHelperProvider.get());
      return instance9;
    }
  }

  private static final class ViewModelCImpl extends BatteryApplication_HiltComponents.ViewModelC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ViewModelCImpl viewModelCImpl = this;

    Provider<AnimationViewModel> animationViewModelProvider;

    Provider<AppViewModel> appViewModelProvider;

    Provider<BatteryViewModel> batteryViewModelProvider;

    Provider<DischargeViewModel> dischargeViewModelProvider;

    Provider<HealthViewModel> healthViewModelProvider;

    Provider<LanguageSelectionViewModel> languageSelectionViewModelProvider;

    Provider<SharedNavigationViewModel> sharedNavigationViewModelProvider;

    Provider<StatsChargeViewModel> statsChargeViewModelProvider;

    ViewModelCImpl(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        SavedStateHandle savedStateHandleParam, ViewModelLifecycle viewModelLifecycleParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;

      initialize(savedStateHandleParam, viewModelLifecycleParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final SavedStateHandle savedStateHandleParam,
        final ViewModelLifecycle viewModelLifecycleParam) {
      this.animationViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 0);
      this.appViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 1);
      this.batteryViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 2);
      this.dischargeViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 3);
      this.healthViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 4);
      this.languageSelectionViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 5);
      this.sharedNavigationViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 6);
      this.statsChargeViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 7);
    }

    @Override
    public Map<Class<?>, javax.inject.Provider<ViewModel>> getHiltViewModelMap() {
      return LazyClassKeyMap.<javax.inject.Provider<ViewModel>>of(ImmutableMap.<String, javax.inject.Provider<ViewModel>>builderWithExpectedSize(8).put(AnimationViewModel_HiltModules_BindsModule_Binds_LazyMapKey.lazyClassKeyName, ((Provider) (animationViewModelProvider))).put(AppViewModel_HiltModules_BindsModule_Binds_LazyMapKey.lazyClassKeyName, ((Provider) (appViewModelProvider))).put(BatteryViewModel_HiltModules_BindsModule_Binds_LazyMapKey.lazyClassKeyName, ((Provider) (batteryViewModelProvider))).put(DischargeViewModel_HiltModules_BindsModule_Binds_LazyMapKey.lazyClassKeyName, ((Provider) (dischargeViewModelProvider))).put(HealthViewModel_HiltModules_BindsModule_Binds_LazyMapKey.lazyClassKeyName, ((Provider) (healthViewModelProvider))).put(LanguageSelectionViewModel_HiltModules_BindsModule_Binds_LazyMapKey.lazyClassKeyName, ((Provider) (languageSelectionViewModelProvider))).put(SharedNavigationViewModel_HiltModules_BindsModule_Binds_LazyMapKey.lazyClassKeyName, ((Provider) (sharedNavigationViewModelProvider))).put(StatsChargeViewModel_HiltModules_BindsModule_Binds_LazyMapKey.lazyClassKeyName, ((Provider) (statsChargeViewModelProvider))).build());
    }

    @Override
    public Map<Class<?>, Object> getHiltViewModelAssistedMap() {
      return ImmutableMap.<Class<?>, Object>of();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final ViewModelCImpl viewModelCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          ViewModelCImpl viewModelCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.viewModelCImpl = viewModelCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.tqhit.battery.one.viewmodel.animation.AnimationViewModel
          return (T) new AnimationViewModel(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.animationRepositoryProvider.get());

          case 1: // com.tqhit.battery.one.viewmodel.AppViewModel
          return (T) new AppViewModel(singletonCImpl.appRepositoryProvider.get());

          case 2: // com.tqhit.battery.one.viewmodel.battery.BatteryViewModel
          return (T) new BatteryViewModel(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.batteryRepositoryProvider.get());

          case 3: // com.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel
          return (T) new DischargeViewModel(singletonCImpl.batteryRepositoryProvider2.get(), singletonCImpl.dischargeCalculatorProvider.get(), singletonCImpl.dischargeSessionRepositoryProvider.get(), singletonCImpl.timeConverterProvider.get(), singletonCImpl.enhancedDischargeTimerServiceHelperProvider.get());

          case 4: // com.tqhit.battery.one.features.stats.health.presentation.HealthViewModel
          return (T) new HealthViewModel(singletonCImpl.defaultHealthRepositoryProvider.get());

          case 5: // com.tqhit.battery.one.fragment.onboarding.LanguageSelectionViewModel
          return (T) new LanguageSelectionViewModel();

          case 6: // com.tqhit.battery.one.features.navigation.SharedNavigationViewModel
          return (T) new SharedNavigationViewModel();

          case 7: // com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel
          return (T) new StatsChargeViewModel(singletonCImpl.defaultStatsChargeRepositoryProvider.get(), new CalculateSimpleChargeEstimateUseCase(), singletonCImpl.appRepositoryProvider.get());

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ActivityRetainedCImpl extends BatteryApplication_HiltComponents.ActivityRetainedC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl = this;

    Provider<ActivityRetainedLifecycle> provideActivityRetainedLifecycleProvider;

    ActivityRetainedCImpl(SingletonCImpl singletonCImpl,
        SavedStateHandleHolder savedStateHandleHolderParam) {
      this.singletonCImpl = singletonCImpl;

      initialize(savedStateHandleHolderParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final SavedStateHandleHolder savedStateHandleHolderParam) {
      this.provideActivityRetainedLifecycleProvider = DoubleCheck.provider(new SwitchingProvider<ActivityRetainedLifecycle>(singletonCImpl, activityRetainedCImpl, 0));
    }

    @Override
    public ActivityComponentBuilder activityComponentBuilder() {
      return new ActivityCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public ActivityRetainedLifecycle getActivityRetainedLifecycle() {
      return provideActivityRetainedLifecycleProvider.get();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // dagger.hilt.android.ActivityRetainedLifecycle
          return (T) ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory.provideActivityRetainedLifecycle();

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ServiceCImpl extends BatteryApplication_HiltComponents.ServiceC {
    private final SingletonCImpl singletonCImpl;

    private final ServiceCImpl serviceCImpl = this;

    ServiceCImpl(SingletonCImpl singletonCImpl, Service serviceParam) {
      this.singletonCImpl = singletonCImpl;


    }

    @Override
    public void injectCoreBatteryStatsService(CoreBatteryStatsService arg0) {
      injectCoreBatteryStatsService2(arg0);
    }

    @Override
    public void injectEnhancedDischargeTimerService(EnhancedDischargeTimerService arg0) {
      injectEnhancedDischargeTimerService2(arg0);
    }

    @Override
    public void injectUnifiedBatteryNotificationService(UnifiedBatteryNotificationService arg0) {
      injectUnifiedBatteryNotificationService2(arg0);
    }

    @Override
    public void injectChargingOverlayService(ChargingOverlayService arg0) {
      injectChargingOverlayService2(arg0);
    }

    @CanIgnoreReturnValue
    private CoreBatteryStatsService injectCoreBatteryStatsService2(
        CoreBatteryStatsService instance) {
      CoreBatteryStatsService_MembersInjector.injectCoreBatteryStatsProvider(instance, singletonCImpl.defaultCoreBatteryStatsProvider.get());
      CoreBatteryStatsService_MembersInjector.injectAppRepository(instance, singletonCImpl.appRepositoryProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private EnhancedDischargeTimerService injectEnhancedDischargeTimerService2(
        EnhancedDischargeTimerService instance2) {
      EnhancedDischargeTimerService_MembersInjector.injectDischargeSessionRepository(instance2, singletonCImpl.dischargeSessionRepositoryProvider.get());
      EnhancedDischargeTimerService_MembersInjector.injectScreenTimeValidationService(instance2, singletonCImpl.screenTimeValidationServiceProvider.get());
      EnhancedDischargeTimerService_MembersInjector.injectAppLifecycleManager(instance2, singletonCImpl.appLifecycleManagerProvider.get());
      return instance2;
    }

    @CanIgnoreReturnValue
    private UnifiedBatteryNotificationService injectUnifiedBatteryNotificationService2(
        UnifiedBatteryNotificationService instance3) {
      UnifiedBatteryNotificationService_MembersInjector.injectCoreBatteryStatsProvider(instance3, singletonCImpl.defaultCoreBatteryStatsProvider.get());
      UnifiedBatteryNotificationService_MembersInjector.injectAppRepository(instance3, singletonCImpl.appRepositoryProvider.get());
      return instance3;
    }

    @CanIgnoreReturnValue
    private ChargingOverlayService injectChargingOverlayService2(ChargingOverlayService instance4) {
      ChargingOverlayService_MembersInjector.injectAppRepository(instance4, singletonCImpl.appRepositoryProvider.get());
      ChargingOverlayService_MembersInjector.injectAnimationRepository(instance4, singletonCImpl.animationRepositoryProvider.get());
      return instance4;
    }
  }

  private static final class SingletonCImpl extends BatteryApplication_HiltComponents.SingletonC {
    private final ApplicationContextModule applicationContextModule;

    private final SingletonCImpl singletonCImpl = this;

    Provider<AdmobConsentHelper> provideAdmobConsentHelperProvider;

    Provider<FirebaseAnalytics> provideFirebaseAnalyticsProvider;

    Provider<FirebaseAnalyticsHelper> provideFirebaseAnalyticsHelperProvider;

    Provider<AdjustAnalyticsHelper> provideAdjustAnalyticsHelperProvider;

    Provider<AnalyticsTracker> provideTrackingManagerProvider;

    Provider<BannerHelper> provideBannerHelperProvider;

    Provider<InterstitialHelper> provideInterstitialHelperProvider;

    Provider<RewardHelper> provideRewardHelperProvider;

    Provider<NativeHelper> provideNativeHelperProvider;

    Provider<FirebaseRemoteConfig> provideFirebaseRemoteConfigProvider;

    Provider<FirebaseRemoteConfigHelper> provideFirebaseRemoteConfigHelperProvider;

    Provider<AppOpenHelper> provideAppOpenHelperProvider;

    Provider<AdmobHelper> provideAdmobHelperProvider;

    Provider<SharedPreferences> provideSharedPreferencesProvider;

    Provider<Gson> provideGsonProvider;

    Provider<PreferencesHelper> providePreferencesHelperProvider;

    Provider<AppRepository> appRepositoryProvider;

    Provider<ApplovinNativeAdManager> applovinNativeAdManagerProvider;

    Provider<ApplovinRewardedAdManager> applovinRewardedAdManagerProvider;

    Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider;

    Provider<ApplovinBannerAdManager> applovinBannerAdManagerProvider;

    Provider<ApplovinAppOpenAdManager> applovinAppOpenAdManagerProvider;

    Provider<CoreBatteryServiceHelper> coreBatteryServiceHelperProvider;

    Provider<AnimationRepository> animationRepositoryProvider;

    Provider<ChargingOverlayServiceHelper> chargingOverlayServiceHelperProvider;

    Provider<AnimationFileManager> animationFileManagerProvider;

    Provider<AnimationPreloader> animationPreloaderProvider;

    Provider<AnimationPreloadingRepository> animationPreloadingRepositoryProvider;

    Provider<AnimationDataService> animationDataServiceProvider;

    Provider<ThumbnailFileManager> provideThumbnailFileManagerProvider;

    Provider<ThumbnailPreloader> provideThumbnailPreloaderProvider;

    Provider<ThumbnailPreloadingRepository> provideThumbnailPreloadingRepositoryProvider;

    Provider<PreloadingMonitor> preloadingMonitorProvider;

    Provider<ThumbnailDataService> provideThumbnailDataServiceProvider;

    Provider<DeferredThumbnailPreloadingService> provideDeferredThumbnailPreloadingServiceProvider;

    Provider<DefaultCoreBatteryStatsProvider> defaultCoreBatteryStatsProvider;

    Provider<PrefsStatsChargeCache> prefsStatsChargeCacheProvider;

    Provider<DefaultStatsChargeRepository> defaultStatsChargeRepositoryProvider;

    Provider<EnhancedDischargeTimerServiceHelper> enhancedDischargeTimerServiceHelperProvider;

    Provider<PrefsCurrentSessionCache> prefsCurrentSessionCacheProvider;

    Provider<TimeConverter> timeConverterProvider;

    Provider<DischargeRateCalculator> dischargeRateCalculatorProvider;

    Provider<SessionMetricsCalculator> sessionMetricsCalculatorProvider;

    Provider<ScreenTimeCalculator> screenTimeCalculatorProvider;

    Provider<DischargeCalculator> dischargeCalculatorProvider;

    Provider<SessionManager> sessionManagerProvider;

    Provider<PrefsDischargeRatesCache> prefsDischargeRatesCacheProvider;

    Provider<GapEstimationCalculator> gapEstimationCalculatorProvider;

    Provider<FullSessionReEstimator> fullSessionReEstimatorProvider;

    Provider<ScreenStateReceiver> provideScreenStateReceiverProvider;

    Provider<DischargeSessionRepository> dischargeSessionRepositoryProvider;

    Provider<UnifiedBatteryNotificationServiceHelper> unifiedBatteryNotificationServiceHelperProvider;

    Provider<AppUsageStatsRepository> appUsageStatsRepositoryProvider;

    Provider<UsageStatsPermissionManager> usageStatsPermissionManagerProvider;

    Provider<DynamicNavigationManager> dynamicNavigationManagerProvider;

    Provider<AppNavigator> appNavigatorProvider;

    Provider<NavigationHandler> navigationHandlerProvider;

    Provider<AppLifecycleManager> appLifecycleManagerProvider;

    Provider<ServiceManager> serviceManagerProvider;

    Provider<FragmentLifecycleManager> fragmentLifecycleManagerProvider;

    Provider<VideoUtils> videoUtilsProvider;

    Provider<ServiceInitializationHelper> serviceInitializationHelperProvider;

    Provider<InitializationProgressManager> initializationProgressManagerProvider;

    Provider<VibrationService> vibrationServiceProvider;

    Provider<AppPowerConsumptionDialogFactory> appPowerConsumptionDialogFactoryProvider;

    Provider<ChargingSessionManager> chargingSessionManagerProvider;

    Provider<HistoryBatteryRepository> historyBatteryRepositoryProvider;

    Provider<DischargeSessionManager> dischargeSessionManagerProvider;

    Provider<BatteryHistoryManager> batteryHistoryManagerProvider;

    Provider<TemperatureHistoryManager> temperatureHistoryManagerProvider;

    Provider<BatteryRepository> batteryRepositoryProvider;

    Provider<com.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository> batteryRepositoryProvider2;

    Provider<DefaultHealthCache> defaultHealthCacheProvider;

    Provider<DefaultHealthRepository> defaultHealthRepositoryProvider;

    Provider<ScreenTimeValidationService> screenTimeValidationServiceProvider;

    SingletonCImpl(ApplicationContextModule applicationContextModuleParam) {
      this.applicationContextModule = applicationContextModuleParam;
      initialize(applicationContextModuleParam);
      initialize2(applicationContextModuleParam);
      initialize3(applicationContextModuleParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final ApplicationContextModule applicationContextModuleParam) {
      this.provideAdmobConsentHelperProvider = DoubleCheck.provider(new SwitchingProvider<AdmobConsentHelper>(singletonCImpl, 2));
      this.provideFirebaseAnalyticsProvider = DoubleCheck.provider(new SwitchingProvider<FirebaseAnalytics>(singletonCImpl, 5));
      this.provideFirebaseAnalyticsHelperProvider = DoubleCheck.provider(new SwitchingProvider<FirebaseAnalyticsHelper>(singletonCImpl, 4));
      this.provideAdjustAnalyticsHelperProvider = DoubleCheck.provider(new SwitchingProvider<AdjustAnalyticsHelper>(singletonCImpl, 6));
      this.provideTrackingManagerProvider = DoubleCheck.provider(new SwitchingProvider<AnalyticsTracker>(singletonCImpl, 3));
      this.provideBannerHelperProvider = DoubleCheck.provider(new SwitchingProvider<BannerHelper>(singletonCImpl, 1));
      this.provideInterstitialHelperProvider = DoubleCheck.provider(new SwitchingProvider<InterstitialHelper>(singletonCImpl, 7));
      this.provideRewardHelperProvider = DoubleCheck.provider(new SwitchingProvider<RewardHelper>(singletonCImpl, 8));
      this.provideNativeHelperProvider = DoubleCheck.provider(new SwitchingProvider<NativeHelper>(singletonCImpl, 9));
      this.provideFirebaseRemoteConfigProvider = DoubleCheck.provider(new SwitchingProvider<FirebaseRemoteConfig>(singletonCImpl, 12));
      this.provideFirebaseRemoteConfigHelperProvider = DoubleCheck.provider(new SwitchingProvider<FirebaseRemoteConfigHelper>(singletonCImpl, 11));
      this.provideAppOpenHelperProvider = DoubleCheck.provider(new SwitchingProvider<AppOpenHelper>(singletonCImpl, 10));
      this.provideAdmobHelperProvider = DoubleCheck.provider(new SwitchingProvider<AdmobHelper>(singletonCImpl, 0));
      this.provideSharedPreferencesProvider = DoubleCheck.provider(new SwitchingProvider<SharedPreferences>(singletonCImpl, 14));
      this.provideGsonProvider = DoubleCheck.provider(new SwitchingProvider<Gson>(singletonCImpl, 15));
      this.providePreferencesHelperProvider = DoubleCheck.provider(new SwitchingProvider<PreferencesHelper>(singletonCImpl, 13));
      this.appRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<AppRepository>(singletonCImpl, 16));
      this.applovinNativeAdManagerProvider = DoubleCheck.provider(new SwitchingProvider<ApplovinNativeAdManager>(singletonCImpl, 17));
      this.applovinRewardedAdManagerProvider = DoubleCheck.provider(new SwitchingProvider<ApplovinRewardedAdManager>(singletonCImpl, 18));
      this.applovinInterstitialAdManagerProvider = DoubleCheck.provider(new SwitchingProvider<ApplovinInterstitialAdManager>(singletonCImpl, 19));
      this.applovinBannerAdManagerProvider = DoubleCheck.provider(new SwitchingProvider<ApplovinBannerAdManager>(singletonCImpl, 20));
      this.applovinAppOpenAdManagerProvider = DoubleCheck.provider(new SwitchingProvider<ApplovinAppOpenAdManager>(singletonCImpl, 21));
      this.coreBatteryServiceHelperProvider = DoubleCheck.provider(new SwitchingProvider<CoreBatteryServiceHelper>(singletonCImpl, 22));
      this.animationRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<AnimationRepository>(singletonCImpl, 24));
      this.chargingOverlayServiceHelperProvider = DoubleCheck.provider(new SwitchingProvider<ChargingOverlayServiceHelper>(singletonCImpl, 23));
    }

    @SuppressWarnings("unchecked")
    private void initialize2(final ApplicationContextModule applicationContextModuleParam) {
      this.animationFileManagerProvider = DoubleCheck.provider(new SwitchingProvider<AnimationFileManager>(singletonCImpl, 27));
      this.animationPreloaderProvider = DoubleCheck.provider(new SwitchingProvider<AnimationPreloader>(singletonCImpl, 26));
      this.animationPreloadingRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<AnimationPreloadingRepository>(singletonCImpl, 25));
      this.animationDataServiceProvider = DoubleCheck.provider(new SwitchingProvider<AnimationDataService>(singletonCImpl, 28));
      this.provideThumbnailFileManagerProvider = DoubleCheck.provider(new SwitchingProvider<ThumbnailFileManager>(singletonCImpl, 32));
      this.provideThumbnailPreloaderProvider = DoubleCheck.provider(new SwitchingProvider<ThumbnailPreloader>(singletonCImpl, 31));
      this.provideThumbnailPreloadingRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<ThumbnailPreloadingRepository>(singletonCImpl, 30));
      this.preloadingMonitorProvider = DoubleCheck.provider(new SwitchingProvider<PreloadingMonitor>(singletonCImpl, 29));
      this.provideThumbnailDataServiceProvider = DoubleCheck.provider(new SwitchingProvider<ThumbnailDataService>(singletonCImpl, 34));
      this.provideDeferredThumbnailPreloadingServiceProvider = DoubleCheck.provider(new SwitchingProvider<DeferredThumbnailPreloadingService>(singletonCImpl, 33));
      this.defaultCoreBatteryStatsProvider = DoubleCheck.provider(new SwitchingProvider<DefaultCoreBatteryStatsProvider>(singletonCImpl, 36));
      this.prefsStatsChargeCacheProvider = DoubleCheck.provider(new SwitchingProvider<PrefsStatsChargeCache>(singletonCImpl, 37));
      this.defaultStatsChargeRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<DefaultStatsChargeRepository>(singletonCImpl, 35));
      this.enhancedDischargeTimerServiceHelperProvider = DoubleCheck.provider(new SwitchingProvider<EnhancedDischargeTimerServiceHelper>(singletonCImpl, 38));
      this.prefsCurrentSessionCacheProvider = DoubleCheck.provider(new SwitchingProvider<PrefsCurrentSessionCache>(singletonCImpl, 40));
      this.timeConverterProvider = DoubleCheck.provider(new SwitchingProvider<TimeConverter>(singletonCImpl, 43));
      this.dischargeRateCalculatorProvider = DoubleCheck.provider(new SwitchingProvider<DischargeRateCalculator>(singletonCImpl, 44));
      this.sessionMetricsCalculatorProvider = DoubleCheck.provider(new SwitchingProvider<SessionMetricsCalculator>(singletonCImpl, 42));
      this.screenTimeCalculatorProvider = DoubleCheck.provider(new SwitchingProvider<ScreenTimeCalculator>(singletonCImpl, 45));
      this.dischargeCalculatorProvider = DoubleCheck.provider(new SwitchingProvider<DischargeCalculator>(singletonCImpl, 46));
      this.sessionManagerProvider = DoubleCheck.provider(new SwitchingProvider<SessionManager>(singletonCImpl, 41));
      this.prefsDischargeRatesCacheProvider = DoubleCheck.provider(new SwitchingProvider<PrefsDischargeRatesCache>(singletonCImpl, 48));
      this.gapEstimationCalculatorProvider = DoubleCheck.provider(new SwitchingProvider<GapEstimationCalculator>(singletonCImpl, 47));
      this.fullSessionReEstimatorProvider = DoubleCheck.provider(new SwitchingProvider<FullSessionReEstimator>(singletonCImpl, 49));
      this.provideScreenStateReceiverProvider = DoubleCheck.provider(new SwitchingProvider<ScreenStateReceiver>(singletonCImpl, 50));
    }

    @SuppressWarnings("unchecked")
    private void initialize3(final ApplicationContextModule applicationContextModuleParam) {
      this.dischargeSessionRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<DischargeSessionRepository>(singletonCImpl, 39));
      this.unifiedBatteryNotificationServiceHelperProvider = DoubleCheck.provider(new SwitchingProvider<UnifiedBatteryNotificationServiceHelper>(singletonCImpl, 51));
      this.appUsageStatsRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<AppUsageStatsRepository>(singletonCImpl, 53));
      this.usageStatsPermissionManagerProvider = DoubleCheck.provider(new SwitchingProvider<UsageStatsPermissionManager>(singletonCImpl, 52));
      this.dynamicNavigationManagerProvider = DoubleCheck.provider(new SwitchingProvider<DynamicNavigationManager>(singletonCImpl, 54));
      this.appNavigatorProvider = DoubleCheck.provider(new SwitchingProvider<AppNavigator>(singletonCImpl, 55));
      this.navigationHandlerProvider = DoubleCheck.provider(new SwitchingProvider<NavigationHandler>(singletonCImpl, 56));
      this.appLifecycleManagerProvider = DoubleCheck.provider(new SwitchingProvider<AppLifecycleManager>(singletonCImpl, 58));
      this.serviceManagerProvider = DoubleCheck.provider(new SwitchingProvider<ServiceManager>(singletonCImpl, 57));
      this.fragmentLifecycleManagerProvider = DoubleCheck.provider(new SwitchingProvider<FragmentLifecycleManager>(singletonCImpl, 59));
      this.videoUtilsProvider = DoubleCheck.provider(new SwitchingProvider<VideoUtils>(singletonCImpl, 60));
      this.serviceInitializationHelperProvider = DoubleCheck.provider(new SwitchingProvider<ServiceInitializationHelper>(singletonCImpl, 62));
      this.initializationProgressManagerProvider = DoubleCheck.provider(new SwitchingProvider<InitializationProgressManager>(singletonCImpl, 61));
      this.vibrationServiceProvider = DoubleCheck.provider(new SwitchingProvider<VibrationService>(singletonCImpl, 63));
      this.appPowerConsumptionDialogFactoryProvider = DoubleCheck.provider(new SwitchingProvider<AppPowerConsumptionDialogFactory>(singletonCImpl, 64));
      this.chargingSessionManagerProvider = DoubleCheck.provider(new SwitchingProvider<ChargingSessionManager>(singletonCImpl, 65));
      this.historyBatteryRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<HistoryBatteryRepository>(singletonCImpl, 66));
      this.dischargeSessionManagerProvider = DoubleCheck.provider(new SwitchingProvider<DischargeSessionManager>(singletonCImpl, 68));
      this.batteryHistoryManagerProvider = DoubleCheck.provider(new SwitchingProvider<BatteryHistoryManager>(singletonCImpl, 69));
      this.temperatureHistoryManagerProvider = DoubleCheck.provider(new SwitchingProvider<TemperatureHistoryManager>(singletonCImpl, 70));
      this.batteryRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<BatteryRepository>(singletonCImpl, 67));
      this.batteryRepositoryProvider2 = DoubleCheck.provider(new SwitchingProvider<com.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository>(singletonCImpl, 71));
      this.defaultHealthCacheProvider = DoubleCheck.provider(new SwitchingProvider<DefaultHealthCache>(singletonCImpl, 73));
      this.defaultHealthRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<DefaultHealthRepository>(singletonCImpl, 72));
      this.screenTimeValidationServiceProvider = DoubleCheck.provider(new SwitchingProvider<ScreenTimeValidationService>(singletonCImpl, 74));
    }

    @Override
    public void injectBatteryApplication(BatteryApplication batteryApplication) {
      injectBatteryApplication2(batteryApplication);
    }

    @Override
    public ApplovinNativeAdManager applovinNativeAdManager() {
      return applovinNativeAdManagerProvider.get();
    }

    @Override
    public Set<Boolean> getDisableFragmentGetContextFix() {
      return ImmutableSet.<Boolean>of();
    }

    @Override
    public ActivityRetainedComponentBuilder retainedComponentBuilder() {
      return new ActivityRetainedCBuilder(singletonCImpl);
    }

    @Override
    public ServiceComponentBuilder serviceComponentBuilder() {
      return new ServiceCBuilder(singletonCImpl);
    }

    @CanIgnoreReturnValue
    private BatteryApplication injectBatteryApplication2(BatteryApplication instance) {
      AdLibHiltApplication_MembersInjector.injectAdmobHelper(instance, provideAdmobHelperProvider.get());
      AdLibHiltApplication_MembersInjector.injectAppOpenHelper(instance, provideAppOpenHelperProvider.get());
      AdLibHiltApplication_MembersInjector.injectAnalyticsHelper(instance, provideAdjustAnalyticsHelperProvider.get());
      AdLibHiltApplication_MembersInjector.injectAnalyticsTracker(instance, provideTrackingManagerProvider.get());
      AdLibHiltApplication_MembersInjector.injectRemoteConfigHelper(instance, provideFirebaseRemoteConfigHelperProvider.get());
      BatteryApplication_MembersInjector.injectPreferencesHelper(instance, providePreferencesHelperProvider.get());
      BatteryApplication_MembersInjector.injectAppRepository(instance, appRepositoryProvider.get());
      BatteryApplication_MembersInjector.injectApplovinNativeAdManager(instance, applovinNativeAdManagerProvider.get());
      BatteryApplication_MembersInjector.injectApplovinRewardedAdManager(instance, applovinRewardedAdManagerProvider.get());
      BatteryApplication_MembersInjector.injectApplovinInterstitialAdManager(instance, applovinInterstitialAdManagerProvider.get());
      BatteryApplication_MembersInjector.injectApplovinBannerAdManager(instance, applovinBannerAdManagerProvider.get());
      BatteryApplication_MembersInjector.injectApplovinAppOpenAdManager(instance, applovinAppOpenAdManagerProvider.get());
      BatteryApplication_MembersInjector.injectCoreBatteryServiceHelper(instance, coreBatteryServiceHelperProvider.get());
      BatteryApplication_MembersInjector.injectChargingOverlayServiceHelper(instance, chargingOverlayServiceHelperProvider.get());
      BatteryApplication_MembersInjector.injectAnimationPreloadingRepository(instance, animationPreloadingRepositoryProvider.get());
      BatteryApplication_MembersInjector.injectAnimationDataService(instance, animationDataServiceProvider.get());
      BatteryApplication_MembersInjector.injectPreloadingMonitor(instance, preloadingMonitorProvider.get());
      BatteryApplication_MembersInjector.injectDeferredThumbnailPreloadingService(instance, provideDeferredThumbnailPreloadingServiceProvider.get());
      return instance;
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.tqhit.adlib.sdk.ads.AdmobHelper
          return (T) AdmobModule_ProvideAdmobHelperFactory.provideAdmobHelper(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideBannerHelperProvider.get(), singletonCImpl.provideInterstitialHelperProvider.get(), singletonCImpl.provideRewardHelperProvider.get(), singletonCImpl.provideNativeHelperProvider.get(), singletonCImpl.provideAppOpenHelperProvider.get());

          case 1: // com.tqhit.adlib.sdk.ads.BannerHelper
          return (T) AdmobModule_ProvideBannerHelperFactory.provideBannerHelper(singletonCImpl.provideAdmobConsentHelperProvider.get(), singletonCImpl.provideTrackingManagerProvider.get());

          case 2: // com.tqhit.adlib.sdk.ads.AdmobConsentHelper
          return (T) AdmobModule_ProvideAdmobConsentHelperFactory.provideAdmobConsentHelper(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 3: // com.tqhit.adlib.sdk.analytics.AnalyticsTracker
          return (T) AnalyticsModule_ProvideTrackingManagerFactory.provideTrackingManager(singletonCImpl.provideFirebaseAnalyticsHelperProvider.get(), singletonCImpl.provideAdjustAnalyticsHelperProvider.get());

          case 4: // com.tqhit.adlib.sdk.firebase.FirebaseAnalyticsHelper
          return (T) FirebaseModule_ProvideFirebaseAnalyticsHelperFactory.provideFirebaseAnalyticsHelper(singletonCImpl.provideFirebaseAnalyticsProvider.get());

          case 5: // com.google.firebase.analytics.FirebaseAnalytics
          return (T) FirebaseModule_ProvideFirebaseAnalyticsFactory.provideFirebaseAnalytics(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 6: // com.tqhit.adlib.sdk.adjust.AdjustAnalyticsHelper
          return (T) AnalyticsModule_ProvideAdjustAnalyticsHelperFactory.provideAdjustAnalyticsHelper(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 7: // com.tqhit.adlib.sdk.ads.InterstitialHelper
          return (T) AdmobModule_ProvideInterstitialHelperFactory.provideInterstitialHelper(singletonCImpl.provideAdmobConsentHelperProvider.get(), singletonCImpl.provideTrackingManagerProvider.get());

          case 8: // com.tqhit.adlib.sdk.ads.RewardHelper
          return (T) AdmobModule_ProvideRewardHelperFactory.provideRewardHelper(singletonCImpl.provideAdmobConsentHelperProvider.get(), singletonCImpl.provideTrackingManagerProvider.get());

          case 9: // com.tqhit.adlib.sdk.ads.NativeHelper
          return (T) AdmobModule_ProvideNativeHelperFactory.provideNativeHelper(singletonCImpl.provideAdmobConsentHelperProvider.get(), singletonCImpl.provideTrackingManagerProvider.get());

          case 10: // com.tqhit.adlib.sdk.ads.AppOpenHelper
          return (T) AdmobModule_ProvideAppOpenHelperFactory.provideAppOpenHelper(singletonCImpl.provideAdmobConsentHelperProvider.get(), singletonCImpl.provideTrackingManagerProvider.get(), singletonCImpl.provideFirebaseRemoteConfigHelperProvider.get());

          case 11: // com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
          return (T) FirebaseModule_ProvideFirebaseRemoteConfigHelperFactory.provideFirebaseRemoteConfigHelper(singletonCImpl.provideFirebaseRemoteConfigProvider.get());

          case 12: // com.google.firebase.remoteconfig.FirebaseRemoteConfig
          return (T) FirebaseModule_ProvideFirebaseRemoteConfigFactory.provideFirebaseRemoteConfig();

          case 13: // com.tqhit.adlib.sdk.data.local.PreferencesHelper
          return (T) SharedPreferencesModule_ProvidePreferencesHelperFactory.providePreferencesHelper(singletonCImpl.provideSharedPreferencesProvider.get(), singletonCImpl.provideGsonProvider.get());

          case 14: // android.content.SharedPreferences
          return (T) SharedPreferencesModule_ProvideSharedPreferencesFactory.provideSharedPreferences(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 15: // com.google.gson.Gson
          return (T) SharedPreferencesModule_ProvideGsonFactory.provideGson();

          case 16: // com.tqhit.battery.one.repository.AppRepository
          return (T) new AppRepository(singletonCImpl.providePreferencesHelperProvider.get(), ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 17: // com.tqhit.battery.one.ads.core.ApplovinNativeAdManager
          return (T) new ApplovinNativeAdManager(singletonCImpl.provideFirebaseRemoteConfigHelperProvider.get(), ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideTrackingManagerProvider.get());

          case 18: // com.tqhit.battery.one.ads.core.ApplovinRewardedAdManager
          return (T) new ApplovinRewardedAdManager(singletonCImpl.provideTrackingManagerProvider.get());

          case 19: // com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager
          return (T) new ApplovinInterstitialAdManager(singletonCImpl.provideFirebaseRemoteConfigHelperProvider.get(), singletonCImpl.applovinRewardedAdManagerProvider.get(), singletonCImpl.provideTrackingManagerProvider.get());

          case 20: // com.tqhit.battery.one.ads.core.ApplovinBannerAdManager
          return (T) new ApplovinBannerAdManager(singletonCImpl.provideTrackingManagerProvider.get());

          case 21: // com.tqhit.battery.one.ads.core.ApplovinAppOpenAdManager
          return (T) new ApplovinAppOpenAdManager(singletonCImpl.provideFirebaseRemoteConfigHelperProvider.get(), singletonCImpl.provideTrackingManagerProvider.get());

          case 22: // com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryServiceHelper
          return (T) new CoreBatteryServiceHelper(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 23: // com.tqhit.battery.one.service.ChargingOverlayServiceHelper
          return (T) new ChargingOverlayServiceHelper(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.appRepositoryProvider.get(), singletonCImpl.animationRepositoryProvider.get());

          case 24: // com.tqhit.battery.one.repository.AnimationRepository
          return (T) new AnimationRepository(singletonCImpl.providePreferencesHelperProvider.get());

          case 25: // com.tqhit.battery.one.repository.AnimationPreloadingRepository
          return (T) new AnimationPreloadingRepository(singletonCImpl.providePreferencesHelperProvider.get(), singletonCImpl.animationPreloaderProvider.get(), singletonCImpl.animationFileManagerProvider.get(), singletonCImpl.provideGsonProvider.get());

          case 26: // com.tqhit.battery.one.service.animation.AnimationPreloader
          return (T) new AnimationPreloader(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.animationFileManagerProvider.get());

          case 27: // com.tqhit.battery.one.manager.animation.AnimationFileManager
          return (T) new AnimationFileManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 28: // com.tqhit.battery.one.service.animation.AnimationDataService
          return (T) new AnimationDataService(singletonCImpl.provideFirebaseRemoteConfigHelperProvider.get(), singletonCImpl.provideGsonProvider.get());

          case 29: // com.tqhit.battery.one.utils.PreloadingMonitor
          return (T) new PreloadingMonitor(singletonCImpl.animationPreloadingRepositoryProvider.get(), singletonCImpl.provideThumbnailPreloadingRepositoryProvider.get());

          case 30: // com.tqhit.battery.one.repository.ThumbnailPreloadingRepository
          return (T) ThumbnailPreloadingModule_ProvideThumbnailPreloadingRepositoryFactory.provideThumbnailPreloadingRepository(singletonCImpl.provideThumbnailPreloaderProvider.get(), singletonCImpl.provideThumbnailFileManagerProvider.get(), singletonCImpl.providePreferencesHelperProvider.get(), singletonCImpl.provideGsonProvider.get());

          case 31: // com.tqhit.battery.one.service.thumbnail.ThumbnailPreloader
          return (T) ThumbnailPreloadingModule_ProvideThumbnailPreloaderFactory.provideThumbnailPreloader(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideThumbnailFileManagerProvider.get());

          case 32: // com.tqhit.battery.one.manager.thumbnail.ThumbnailFileManager
          return (T) ThumbnailPreloadingModule_ProvideThumbnailFileManagerFactory.provideThumbnailFileManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 33: // com.tqhit.battery.one.service.thumbnail.DeferredThumbnailPreloadingService
          return (T) ThumbnailPreloadingModule_ProvideDeferredThumbnailPreloadingServiceFactory.provideDeferredThumbnailPreloadingService(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.animationDataServiceProvider.get(), singletonCImpl.provideThumbnailDataServiceProvider.get(), singletonCImpl.provideThumbnailPreloadingRepositoryProvider.get());

          case 34: // com.tqhit.battery.one.service.thumbnail.ThumbnailDataService
          return (T) ThumbnailPreloadingModule_ProvideThumbnailDataServiceFactory.provideThumbnailDataService(singletonCImpl.animationDataServiceProvider.get());

          case 35: // com.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository
          return (T) new DefaultStatsChargeRepository(singletonCImpl.defaultCoreBatteryStatsProvider.get(), singletonCImpl.prefsStatsChargeCacheProvider.get(), singletonCImpl.appRepositoryProvider.get());

          case 36: // com.tqhit.battery.one.features.stats.corebattery.domain.DefaultCoreBatteryStatsProvider
          return (T) new DefaultCoreBatteryStatsProvider();

          case 37: // com.tqhit.battery.one.features.stats.charge.cache.PrefsStatsChargeCache
          return (T) new PrefsStatsChargeCache(singletonCImpl.providePreferencesHelperProvider.get(), singletonCImpl.provideGsonProvider.get());

          case 38: // com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper
          return (T) new EnhancedDischargeTimerServiceHelper(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 39: // com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository
          return (T) new DischargeSessionRepository(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.prefsCurrentSessionCacheProvider.get(), singletonCImpl.sessionManagerProvider.get(), singletonCImpl.gapEstimationCalculatorProvider.get(), singletonCImpl.fullSessionReEstimatorProvider.get(), singletonCImpl.sessionMetricsCalculatorProvider.get(), singletonCImpl.provideScreenStateReceiverProvider.get());

          case 40: // com.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache
          return (T) new PrefsCurrentSessionCache(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideGsonProvider.get());

          case 41: // com.tqhit.battery.one.features.stats.discharge.domain.SessionManager
          return (T) new SessionManager(singletonCImpl.sessionMetricsCalculatorProvider.get(), singletonCImpl.screenTimeCalculatorProvider.get(), singletonCImpl.dischargeRateCalculatorProvider.get(), singletonCImpl.dischargeCalculatorProvider.get());

          case 42: // com.tqhit.battery.one.features.stats.discharge.domain.SessionMetricsCalculator
          return (T) new SessionMetricsCalculator(singletonCImpl.timeConverterProvider.get(), singletonCImpl.dischargeRateCalculatorProvider.get());

          case 43: // com.tqhit.battery.one.features.stats.discharge.domain.TimeConverter
          return (T) new TimeConverter();

          case 44: // com.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator
          return (T) new DischargeRateCalculator(singletonCImpl.timeConverterProvider.get());

          case 45: // com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator
          return (T) new ScreenTimeCalculator(singletonCImpl.timeConverterProvider.get());

          case 46: // com.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator
          return (T) new DischargeCalculator();

          case 47: // com.tqhit.battery.one.features.stats.discharge.domain.GapEstimationCalculator
          return (T) new GapEstimationCalculator(singletonCImpl.sessionMetricsCalculatorProvider.get(), singletonCImpl.screenTimeCalculatorProvider.get(), singletonCImpl.prefsDischargeRatesCacheProvider.get(), singletonCImpl.timeConverterProvider.get(), singletonCImpl.dischargeRateCalculatorProvider.get());

          case 48: // com.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache
          return (T) new PrefsDischargeRatesCache(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 49: // com.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimator
          return (T) new FullSessionReEstimator(singletonCImpl.screenTimeCalculatorProvider.get(), singletonCImpl.prefsDischargeRatesCacheProvider.get(), singletonCImpl.timeConverterProvider.get(), singletonCImpl.dischargeRateCalculatorProvider.get());

          case 50: // com.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver
          return (T) StatsDischargeProvidersModule_ProvideScreenStateReceiverFactory.provideScreenStateReceiver(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 51: // com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationServiceHelper
          return (T) new UnifiedBatteryNotificationServiceHelper(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 52: // com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager
          return (T) new UsageStatsPermissionManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.appUsageStatsRepositoryProvider.get());

          case 53: // com.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository
          return (T) new AppUsageStatsRepository(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 54: // com.tqhit.battery.one.features.navigation.DynamicNavigationManager
          return (T) new DynamicNavigationManager(singletonCImpl.defaultCoreBatteryStatsProvider.get());

          case 55: // com.tqhit.battery.one.features.navigation.AppNavigator
          return (T) new AppNavigator(singletonCImpl.dynamicNavigationManagerProvider.get(), singletonCImpl.defaultCoreBatteryStatsProvider.get());

          case 56: // com.tqhit.battery.one.activity.main.handlers.NavigationHandler
          return (T) new NavigationHandler(singletonCImpl.dynamicNavigationManagerProvider.get());

          case 57: // com.tqhit.battery.one.activity.main.handlers.ServiceManager
          return (T) new ServiceManager(singletonCImpl.unifiedBatteryNotificationServiceHelperProvider.get(), singletonCImpl.enhancedDischargeTimerServiceHelperProvider.get(), singletonCImpl.chargingOverlayServiceHelperProvider.get(), singletonCImpl.appLifecycleManagerProvider.get());

          case 58: // com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager
          return (T) new AppLifecycleManager();

          case 59: // com.tqhit.battery.one.activity.main.handlers.FragmentLifecycleManager
          return (T) new FragmentLifecycleManager(singletonCImpl.dynamicNavigationManagerProvider.get(), singletonCImpl.defaultCoreBatteryStatsProvider.get());

          case 60: // com.tqhit.battery.one.utils.VideoUtils
          return (T) new VideoUtils(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.animationPreloadingRepositoryProvider.get());

          case 61: // com.tqhit.battery.one.initialization.InitializationProgressManager
          return (T) new InitializationProgressManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.serviceInitializationHelperProvider.get());

          case 62: // com.tqhit.battery.one.initialization.ServiceInitializationHelper
          return (T) new ServiceInitializationHelper(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.coreBatteryServiceHelperProvider.get());

          case 63: // com.tqhit.battery.one.service.VibrationService
          return (T) new VibrationService(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 64: // com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialogFactory
          return (T) new AppPowerConsumptionDialogFactory(singletonCImpl.appUsageStatsRepositoryProvider.get(), singletonCImpl.usageStatsPermissionManagerProvider.get());

          case 65: // com.tqhit.battery.one.manager.charge.ChargingSessionManager
          return (T) new ChargingSessionManager(singletonCImpl.providePreferencesHelperProvider.get());

          case 66: // com.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository
          return (T) new HistoryBatteryRepository(singletonCImpl.defaultCoreBatteryStatsProvider.get(), singletonCImpl.providePreferencesHelperProvider.get());

          case 67: // com.tqhit.battery.one.repository.BatteryRepository
          return (T) new BatteryRepository(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.providePreferencesHelperProvider.get(), singletonCImpl.chargingSessionManagerProvider.get(), singletonCImpl.dischargeSessionManagerProvider.get(), singletonCImpl.batteryHistoryManagerProvider.get(), singletonCImpl.temperatureHistoryManagerProvider.get());

          case 68: // com.tqhit.battery.one.manager.discharge.DischargeSessionManager
          return (T) new DischargeSessionManager(singletonCImpl.providePreferencesHelperProvider.get());

          case 69: // com.tqhit.battery.one.manager.graph.BatteryHistoryManager
          return (T) new BatteryHistoryManager(singletonCImpl.providePreferencesHelperProvider.get());

          case 70: // com.tqhit.battery.one.manager.graph.TemperatureHistoryManager
          return (T) new TemperatureHistoryManager(singletonCImpl.providePreferencesHelperProvider.get());

          case 71: // com.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository
          return (T) new com.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.defaultCoreBatteryStatsProvider.get(), singletonCImpl.appRepositoryProvider.get(), singletonCImpl.prefsDischargeRatesCacheProvider.get(), singletonCImpl.dischargeSessionRepositoryProvider.get());

          case 72: // com.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository
          return (T) new DefaultHealthRepository(singletonCImpl.defaultCoreBatteryStatsProvider.get(), singletonCImpl.chargingSessionManagerProvider.get(), singletonCImpl.appRepositoryProvider.get(), singletonCImpl.defaultHealthCacheProvider.get(), singletonCImpl.historyBatteryRepositoryProvider.get());

          case 73: // com.tqhit.battery.one.features.stats.health.cache.DefaultHealthCache
          return (T) new DefaultHealthCache(singletonCImpl.providePreferencesHelperProvider.get());

          case 74: // com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService
          return (T) new ScreenTimeValidationService(singletonCImpl.appLifecycleManagerProvider.get());

          default: throw new AssertionError(id);
        }
      }
    }
  }
}
