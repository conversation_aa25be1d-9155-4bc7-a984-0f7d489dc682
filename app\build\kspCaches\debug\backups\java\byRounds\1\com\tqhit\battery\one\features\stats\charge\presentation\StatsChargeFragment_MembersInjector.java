package com.tqhit.battery.one.features.stats.charge.presentation;

import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager;
import com.tqhit.battery.one.ads.core.ApplovinNativeAdManager;
import com.tqhit.battery.one.features.navigation.AppNavigator;
import com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager;
import com.tqhit.battery.one.service.VibrationService;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class StatsChargeFragment_MembersInjector implements MembersInjector<StatsChargeFragment> {
  private final Provider<VibrationService> vibrationServiceProvider;

  private final Provider<AppNavigator> appNavigatorProvider;

  private final Provider<ApplovinNativeAdManager> applovinNativeAdManagerProvider;

  private final Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider;

  private final Provider<AppLifecycleManager> appLifecycleManagerProvider;

  public StatsChargeFragment_MembersInjector(Provider<VibrationService> vibrationServiceProvider,
      Provider<AppNavigator> appNavigatorProvider,
      Provider<ApplovinNativeAdManager> applovinNativeAdManagerProvider,
      Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider,
      Provider<AppLifecycleManager> appLifecycleManagerProvider) {
    this.vibrationServiceProvider = vibrationServiceProvider;
    this.appNavigatorProvider = appNavigatorProvider;
    this.applovinNativeAdManagerProvider = applovinNativeAdManagerProvider;
    this.applovinInterstitialAdManagerProvider = applovinInterstitialAdManagerProvider;
    this.appLifecycleManagerProvider = appLifecycleManagerProvider;
  }

  public static MembersInjector<StatsChargeFragment> create(
      Provider<VibrationService> vibrationServiceProvider,
      Provider<AppNavigator> appNavigatorProvider,
      Provider<ApplovinNativeAdManager> applovinNativeAdManagerProvider,
      Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider,
      Provider<AppLifecycleManager> appLifecycleManagerProvider) {
    return new StatsChargeFragment_MembersInjector(vibrationServiceProvider, appNavigatorProvider, applovinNativeAdManagerProvider, applovinInterstitialAdManagerProvider, appLifecycleManagerProvider);
  }

  @Override
  public void injectMembers(StatsChargeFragment instance) {
    injectVibrationService(instance, vibrationServiceProvider.get());
    injectAppNavigator(instance, appNavigatorProvider.get());
    injectApplovinNativeAdManager(instance, applovinNativeAdManagerProvider.get());
    injectApplovinInterstitialAdManager(instance, applovinInterstitialAdManagerProvider.get());
    injectAppLifecycleManager(instance, appLifecycleManagerProvider.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment.vibrationService")
  public static void injectVibrationService(StatsChargeFragment instance,
      VibrationService vibrationService) {
    instance.vibrationService = vibrationService;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment.appNavigator")
  public static void injectAppNavigator(StatsChargeFragment instance, AppNavigator appNavigator) {
    instance.appNavigator = appNavigator;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment.applovinNativeAdManager")
  public static void injectApplovinNativeAdManager(StatsChargeFragment instance,
      ApplovinNativeAdManager applovinNativeAdManager) {
    instance.applovinNativeAdManager = applovinNativeAdManager;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment.applovinInterstitialAdManager")
  public static void injectApplovinInterstitialAdManager(StatsChargeFragment instance,
      ApplovinInterstitialAdManager applovinInterstitialAdManager) {
    instance.applovinInterstitialAdManager = applovinInterstitialAdManager;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment.appLifecycleManager")
  public static void injectAppLifecycleManager(StatsChargeFragment instance,
      AppLifecycleManager appLifecycleManager) {
    instance.appLifecycleManager = appLifecycleManager;
  }
}
