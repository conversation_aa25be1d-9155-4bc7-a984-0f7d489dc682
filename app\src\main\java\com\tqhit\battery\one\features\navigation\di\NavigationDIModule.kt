package com.tqhit.battery.one.features.navigation.di

import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

/**
 * Hilt module for navigation feature dependency injection.
 * 
 * This module provides bindings for navigation-related interfaces and implementations
 * following the established stats module architecture pattern.
 * 
 * The AppNavigator is provided as a singleton to ensure consistent navigation state
 * across the application and prevent multiple instances from conflicting.
 * 
 * Dependencies:
 * - DynamicNavigationManager: Already provided by existing DI setup
 * - CoreBatteryStatsProvider: Already provided by existing DI setup
 * 
 * Note: AppNavigator is marked with @Singleton and @Inject constructor,
 * so Hilt will automatically provide it without explicit binding.
 * This module serves as documentation and future extension point.
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class NavigationDIModule {
    
    // AppNavigator is automatically provided by Hilt due to @Singleton and @Inject constructor
    // No explicit binding needed, but this module documents the navigation DI setup
    
    // Future navigation-related bindings can be added here as the system evolves
}
