@echo off
echo Testing Overlay Toggle Functionality on Customize Screen - ENHANCED LOGGING
echo ==============================================================================

echo.
echo 1. Clearing logcat buffer...
adb logcat -c

echo.
echo 2. Launching app...
adb shell am start -W -n com.fc.p.tj.charginganimation.batterycharging.chargeeffect/com.tqhit.battery.one.activity.main.MainActivity

echo.
echo 3. Waiting for app to load...
timeout /t 3 /nobreak > nul

echo.
echo 4. Starting comprehensive logcat monitoring...
echo    - TOGGLE_DEBUG: Detailed toggle functionality tracing
echo    - CustomizeViewModel: ViewModel events and state changes
echo    - CustomizeFragment: UI interactions and updates
echo    - EMOJI_PERMISSION: Permission flow tracking
echo.
echo TESTING INSTRUCTIONS:
echo ===================
echo 1. Navigate to Emoji Gallery (bottom navigation)
echo 2. Select any emoji style to open Customize screen
echo 3. Look for the overlay toggle switch at the top
echo 4. Click the toggle to ENABLE (should show permission dialog)
echo 5. Follow permission flow (grant or deny)
echo 6. Click the toggle to DISABLE
echo 7. Test multiple enable/disable cycles
echo.
echo WHAT TO LOOK FOR IN LOGS:
echo ========================
echo - TOGGLE_DEBUG: Global toggle switch clicked
echo - TOGGLE_DEBUG: ToggleGlobalEnabled event received
echo - TOGGLE_DEBUG: Permission checking results
echo - TOGGLE_DEBUG: Repository update operations
echo - TOGGLE_DEBUG: UI state updates
echo.
echo Press Ctrl+C to stop monitoring when done testing
echo.

adb logcat -s CustomizeViewModel:D CustomizeFragment:D EMOJI_PERMISSION:D EmojiOverlayPermissionManager:D | findstr /C:"TOGGLE_DEBUG" /C:"Global toggle" /C:"ToggleGlobalEnabled" /C:"Permission" /C:"CustomizeViewModel" /C:"CustomizeFragment"
