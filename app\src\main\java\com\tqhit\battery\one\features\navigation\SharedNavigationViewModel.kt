package com.tqhit.battery.one.features.navigation

import android.util.Log
import androidx.lifecycle.ViewModel
import com.tqhit.battery.one.R
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject

/**
 * SharedNavigationViewModel manages the active fragment state across the application.
 * This replaces the complex FragmentLifecycleOptimizer with a cleaner, more maintainable
 * solution using standard Android Jetpack components.
 * 
 * Key Features:
 * - Centralized fragment state management using StateFlow
 * - Self-managed fragment lifecycle through observation
 * - Integration with CoreBatteryStatsService and AppLifecycleManager
 * - Comprehensive debug logging for troubleshooting
 * - Follows stats module architecture pattern
 * 
 * Usage:
 * - DynamicNavigationManager updates the active fragment ID
 * - Fragments observe the StateFlow and self-manage their active/inactive state
 * - No external lifecycle management required
 */
@HiltViewModel
class SharedNavigationViewModel @Inject constructor() : ViewModel() {
    
    companion object {
        private const val TAG = "SharedNavigationViewModel"
        
        // Default fragment ID (Animation Grid Fragment)
        private val DEFAULT_FRAGMENT_ID = R.id.animationGridFragment
    }
    
    // StateFlow for active fragment ID tracking
    private val _activeFragmentId = MutableStateFlow(DEFAULT_FRAGMENT_ID)
    val activeFragmentId: StateFlow<Int> = _activeFragmentId.asStateFlow()
    
    // StateFlow for fragment transition events
    private val _fragmentTransition = MutableStateFlow<FragmentTransition?>(null)
    val fragmentTransition: StateFlow<FragmentTransition?> = _fragmentTransition.asStateFlow()
    
    // Performance tracking
    private var transitionCount = 0
    private var lastTransitionTime = 0L
    
    init {
        Log.d(TAG, "SharedNavigationViewModel: ViewModel initialized with default fragment: ${getFragmentName(DEFAULT_FRAGMENT_ID)}")
        Log.d(TAG, "SharedNavigationViewModel: Ready to manage fragment navigation state")
    }
    
    /**
     * Sets the active fragment ID and notifies all observers.
     * This method is called by DynamicNavigationManager when fragment switching occurs.
     * 
     * @param fragmentId The ID of the fragment that should become active
     * @param reason The reason for the fragment change (for debugging)
     */
    fun setActiveFragment(fragmentId: Int, reason: String = "Unknown") {
        val previousFragmentId = _activeFragmentId.value
        val currentTime = System.currentTimeMillis()
        
        // Don't update if the fragment hasn't actually changed
        if (previousFragmentId == fragmentId) {
            Log.v(TAG, "SharedNavigationViewModel: Fragment ID unchanged ($fragmentId), skipping update")
            return
        }
        
        val previousFragmentName = getFragmentName(previousFragmentId)
        val newFragmentName = getFragmentName(fragmentId)
        
        Log.d(TAG, "SharedNavigationViewModel: ═══════════════════════════════════════════════════════════")
        Log.d(TAG, "SharedNavigationViewModel: 🔄 FRAGMENT TRANSITION")
        Log.d(TAG, "SharedNavigationViewModel: ═══════════════════════════════════════════════════════════")
        Log.d(TAG, "SharedNavigationViewModel: From: $previousFragmentName (ID: $previousFragmentId)")
        Log.d(TAG, "SharedNavigationViewModel: To: $newFragmentName (ID: $fragmentId)")
        Log.d(TAG, "SharedNavigationViewModel: Reason: $reason")
        Log.d(TAG, "SharedNavigationViewModel: Timestamp: $currentTime")
        Log.d(TAG, "SharedNavigationViewModel: Transition count: ${transitionCount + 1}")
        
        // Calculate transition timing
        val timeSinceLastTransition = if (lastTransitionTime > 0) {
            currentTime - lastTransitionTime
        } else {
            0L
        }
        
        if (timeSinceLastTransition > 0) {
            Log.d(TAG, "SharedNavigationViewModel: Time since last transition: ${timeSinceLastTransition}ms")
        }
        
        // Update the active fragment ID
        _activeFragmentId.value = fragmentId
        
        // Create and emit transition event
        val transition = FragmentTransition(
            fromFragmentId = previousFragmentId,
            toFragmentId = fragmentId,
            reason = reason,
            timestamp = currentTime
        )
        _fragmentTransition.value = transition
        
        // Update performance tracking
        transitionCount++
        lastTransitionTime = currentTime
        
        Log.d(TAG, "SharedNavigationViewModel: ✅ Fragment transition completed")
        Log.d(TAG, "SharedNavigationViewModel: Active fragment updated to: $newFragmentName")
        Log.d(TAG, "SharedNavigationViewModel: Observers will be notified of state change")
        Log.d(TAG, "SharedNavigationViewModel: ═══════════════════════════════════════════════════════════")
    }
    
    /**
     * Gets the current active fragment ID.
     * 
     * @return The ID of the currently active fragment
     */
    fun getCurrentActiveFragmentId(): Int = _activeFragmentId.value
    
    /**
     * Checks if a specific fragment is currently active.
     * 
     * @param fragmentId The fragment ID to check
     * @return true if the fragment is active, false otherwise
     */
    fun isFragmentActive(fragmentId: Int): Boolean {
        val isActive = _activeFragmentId.value == fragmentId
        Log.v(TAG, "SharedNavigationViewModel: Fragment ${getFragmentName(fragmentId)} active check: $isActive")
        return isActive
    }
    
    /**
     * Gets performance statistics for debugging.
     * 
     * @return A string containing performance statistics
     */
    fun getPerformanceStats(): String {
        val currentFragmentName = getFragmentName(_activeFragmentId.value)
        val timeSinceLastTransition = if (lastTransitionTime > 0) {
            System.currentTimeMillis() - lastTransitionTime
        } else {
            0L
        }
        
        return "SharedNavigationViewModel Stats - " +
                "Active: $currentFragmentName, " +
                "Transitions: $transitionCount, " +
                "Last transition: ${timeSinceLastTransition}ms ago"
    }
    
    /**
     * Gets a human-readable fragment name for logging.
     * 
     * @param fragmentId The fragment ID
     * @return A human-readable fragment name
     */
    private fun getFragmentName(fragmentId: Int): String {
        return when (fragmentId) {
            R.id.chargeFragment -> "ChargeFragment"
            R.id.dischargeFragment -> "DischargeFragment"
            R.id.animationGridFragment -> "AnimationGridFragment"
            R.id.othersFragment -> "OthersFragment"
            R.id.healthFragment -> "HealthFragment"
            R.id.settingsFragment -> "SettingsFragment"
            else -> "Unknown($fragmentId)"
        }
    }
    
    override fun onCleared() {
        super.onCleared()
        Log.d(TAG, "SharedNavigationViewModel: ViewModel cleared")
        Log.d(TAG, "SharedNavigationViewModel: Final stats - ${getPerformanceStats()}")
    }
}

/**
 * Represents a fragment transition event.
 * Used for debugging and tracking fragment navigation flow.
 */
data class FragmentTransition(
    val fromFragmentId: Int,
    val toFragmentId: Int,
    val reason: String,
    val timestamp: Long
)
