package com.tqhit.battery.one.fragment.main.others.data

import com.applovin.mediation.nativeAds.MaxNativeAdView

/**
 * Data class representing an item in the Others fragment RecyclerView.
 * Used for the card-based layout showing Charge/Discharge, Battery Health, and Settings options.
 * 
 * @param id Unique identifier for the item (used for navigation routing)
 * @param title Display title for the card
 * @param description Description text shown below the title
 * @param iconResId Resource ID for the card icon
 * @param isEnabled Whether the item is clickable/enabled
 */
sealed class OthersItemData {
    data class Normal(
        val id: String,
        val title: String,
        val description: String,
        val iconResId: Int,
        val isEnabled: Boolean = true
    ) : OthersItemData()

    data class NativeAd(val adView: MaxNativeAdView) : OthersItemData()

    companion object {
        const val CHARGE_DISCHARGE_ITEM_ID = "charge_discharge"
        const val HEALTH_ITEM_ID = "health"
        const val SETTINGS_ITEM_ID = "settings"
        const val NATIVE_AD = "native_ads"

    }
}

