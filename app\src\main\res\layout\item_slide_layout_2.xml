<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

<RelativeLayout
    android:layout_width="match_parent"
    android:layout_height="0dp"
    android:layout_weight="1"
    android:layout_marginVertical="@dimen/_12sdp">
    <LinearLayout
        android:orientation="vertical"
        android:visibility="visible"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true">
        <LinearLayout
            android:gravity="center"
            android:layout_gravity="center"
            android:orientation="vertical"
            android:background="@drawable/ic_privacy_policy"
            android:layout_width="105dp"
            android:layout_height="106dp"/>
        <TextView
            android:textSize="22sp"
            android:textColor="?attr/black"
            android:ellipsize="marquee"
            android:gravity="center"
            android:id="@+id/textView4"
            android:focusableInTouchMode="true"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="@string/Privacy_Policy"
            android:singleLine="true"
            android:marqueeRepeatLimit="marquee_forever"
            android:layout_marginStart="14dp"
            android:layout_marginEnd="14dp"/>
        <TextView
            android:textSize="14sp"
            android:textColor="?attr/black"
            android:gravity="center"
            android:id="@+id/under_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="7dp"
            android:layout_marginBottom="10dp"
            android:text="@string/privacy_policy_starting"
            android:animateLayoutChanges="true"
            android:layout_marginStart="9dp"
            android:layout_marginEnd="7dp"/>
        <LinearLayout
            android:orientation="vertical"
            android:background="@drawable/white_block"
            android:padding="8dp"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:animateLayoutChanges="true"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:elevation="1dp">
            <Button
                android:textColor="?attr/black"
                android:gravity="top|center_horizontal"
                android:id="@+id/privacy_policy_button"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:longClickable="false"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:text="@string/Privacy_Policy"
                android:textAllCaps="false"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                style="@style/Widget.AppCompat.Button.Borderless"/>
            <Button
                android:textColor="?attr/black"
                android:gravity="top|center_horizontal"
                android:id="@+id/confirm_and_continue_button"
                android:background="@drawable/grey_block"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:longClickable="false"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="8dp"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:text="@string/confirm_and_continue"
                android:textAllCaps="false"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                style="@style/Widget.AppCompat.Button.Borderless"/>
        </LinearLayout>
    </LinearLayout>

</RelativeLayout>

    <LinearLayout
        android:layout_gravity="end"
        android:id="@+id/next_page"
        android:visibility="visible"
        android:layout_width="80dp"
        android:layout_height="40dp"
        android:layout_marginBottom="25dp"
        android:layout_marginEnd="30dp"
        android:stateListAnimator="@null"
        android:gravity="center"
        android:orientation="horizontal"
        android:outlineProvider="background">
        <TextView
            android:id="@+id/swipe_text"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/_16sdp"
            android:textStyle="bold"
            android:textSize="@dimen/_14ssp"
            android:layout_marginEnd="@dimen/_3sdp"
            android:textAllCaps="true"
            android:textColor="?attr/black"
            android:text="@string/next"/>
        <ImageView
            android:layout_width="@dimen/_12sdp"
            android:layout_height="@dimen/_12sdp"
            android:src="@drawable/ic_strelka"
            android:contentDescription="@string/next"
            android:scaleX="-1"/>
    </LinearLayout>

    <com.facebook.shimmer.ShimmerFrameLayout
        android:id="@+id/nativeAd"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/grey" />
</LinearLayout>
