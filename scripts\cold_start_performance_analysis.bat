@echo off
REM TJ_BatteryOne Cold Start Performance Analysis Script
REM This script measures and analyzes app cold start performance
REM Application ID: com.fc.p.tj.charginganimation.batterycharging.chargeeffect

setlocal enabledelayedexpansion

set ADB_PATH=E:\IDE\Android\SDK\platform-tools\adb.exe
set APP_ID=com.fc.p.tj.charginganimation.batterycharging.chargeeffect
set MAIN_ACTIVITY=%APP_ID%/com.tqhit.battery.one.activity.activity.splash.SplashActivity
set TIMESTAMP=%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%
set LOG_FILE=cold_start_analysis_%TIMESTAMP%.log

echo ========================================
echo TJ_BatteryOne Cold Start Performance Analysis
echo ========================================
echo Application ID: %APP_ID%
echo Main Activity: %MAIN_ACTIVITY%
echo Log File: %LOG_FILE%
echo Timestamp: %TIMESTAMP%
echo ========================================

REM Check if ADB is available
if not exist "%ADB_PATH%" (
    echo ERROR: ADB not found at %ADB_PATH%
    pause
    exit /b 1
)

REM Check if device is connected
echo Checking device connection...
"%ADB_PATH%" devices > temp_devices.txt
findstr /C:"device" temp_devices.txt > nul
if errorlevel 1 (
    echo ERROR: No Android device connected
    del temp_devices.txt
    pause
    exit /b 1
)
del temp_devices.txt
echo Device connected successfully.

REM Create log file header
echo Cold Start Performance Analysis - %TIMESTAMP% > %LOG_FILE%
echo Application ID: %APP_ID% >> %LOG_FILE%
echo ======================================== >> %LOG_FILE%

echo.
echo Step 1: Force stopping the app to ensure cold start...
"%ADB_PATH%" shell am force-stop %APP_ID%
timeout /t 2 /nobreak > nul

echo Step 2: Clearing logcat buffer...
"%ADB_PATH%" logcat -c

echo Step 3: Starting logcat monitoring in background...
start /b "%ADB_PATH%" logcat -s STARTUP_TIMING:D MAX_INIT:D AD_ADAPTER_LOAD:D SPLASH_PROGRESS:D BatteryApplication:D MainActivity:D SplashActivity:D CoreBatteryStatsService:D InitializationProgressManager:D > logcat_startup_%TIMESTAMP%.log

echo Step 4: Waiting 2 seconds for logcat to initialize...
timeout /t 2 /nobreak > nul

echo Step 5: Measuring cold start time with ADB...
echo ======================================== >> %LOG_FILE%
echo COLD START MEASUREMENT >> %LOG_FILE%
echo ======================================== >> %LOG_FILE%

REM Perform multiple cold start measurements
for /L %%i in (1,1,5) do (
    echo.
    echo === Cold Start Test %%i/5 ===
    echo Cold Start Test %%i/5 >> %LOG_FILE%
    
    REM Force stop app
    "%ADB_PATH%" shell am force-stop %APP_ID%
    timeout /t 3 /nobreak > nul
    
    REM Measure cold start time
    echo Launching app and measuring startup time...
    "%ADB_PATH%" shell am start -W -n %MAIN_ACTIVITY% >> %LOG_FILE% 2>&1
    
    REM Wait for app to fully load
    timeout /t 8 /nobreak > nul
    
    echo Test %%i completed.
    echo ---------------------------------------- >> %LOG_FILE%
)

echo.
echo Step 6: Collecting detailed logcat analysis...
timeout /t 3 /nobreak > nul

REM Stop the background logcat process
taskkill /f /im adb.exe > nul 2>&1

echo Step 7: Analyzing performance logs...
echo ======================================== >> %LOG_FILE%
echo LOGCAT ANALYSIS >> %LOG_FILE%
echo ======================================== >> %LOG_FILE%

REM Append logcat results to main log file
if exist logcat_startup_%TIMESTAMP%.log (
    type logcat_startup_%TIMESTAMP%.log >> %LOG_FILE%
) else (
    echo WARNING: Logcat file not found >> %LOG_FILE%
)

echo.
echo Step 8: Generating performance summary...
echo ======================================== >> %LOG_FILE%
echo PERFORMANCE SUMMARY >> %LOG_FILE%
echo ======================================== >> %LOG_FILE%

REM Extract timing information from logcat
findstr /C:"STARTUP_TIMING" %LOG_FILE% > startup_timings_%TIMESTAMP%.txt
findstr /C:"MAX_INIT" %LOG_FILE% > max_init_timings_%TIMESTAMP%.txt
findstr /C:"SPLASH_PROGRESS" %LOG_FILE% > splash_progress_%TIMESTAMP%.txt

echo Performance analysis completed!
echo.
echo Results saved to: %LOG_FILE%
echo Additional files:
echo - startup_timings_%TIMESTAMP%.txt
echo - max_init_timings_%TIMESTAMP%.txt  
echo - splash_progress_%TIMESTAMP%.txt
echo - logcat_startup_%TIMESTAMP%.log
echo.
echo ========================================
echo ANALYSIS SUMMARY
echo ========================================

REM Display key findings
if exist startup_timings_%TIMESTAMP%.txt (
    echo Key Startup Timings:
    type startup_timings_%TIMESTAMP%.txt | findstr /C:"BatteryApplication.onCreate() completed"
    type startup_timings_%TIMESTAMP%.txt | findstr /C:"MainActivity.onCreate()"
    type startup_timings_%TIMESTAMP%.txt | findstr /C:"Total async initialization"
)

echo.
echo Check %LOG_FILE% for detailed analysis.
echo Target: Cold start should be under 3000ms
echo.
pause
